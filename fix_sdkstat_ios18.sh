#!/bin/bash

echo "=== Correção específica para o SDKStatCaches do iOS 18 ==="

# Corrigir permissões e criar diretório
echo "Criando diretório SDKStatCaches.noindex com permissões corretas..."
mkdir -p ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex
chmod -R 755 ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex

# Criar arquivo de cache vazio
echo "Criando arquivo de cache do iOS 18.0..."
touch ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.0-22A3362-ecbf2746db86eb790eafe45896842e4a.sdkstatcache
chmod 644 ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.0-22A3362-ecbf2746db86eb790eafe45896842e4a.sdkstatcache

# Limpar arquivos obsoletos
echo "Limpando arquivos obsoletos..."
rm -rf ~/Library/Developer/Xcode/DerivedData/*Cache* 2>/dev/null || true

# Redefinir o projeto
echo "Redefinindo o projeto..."
cd ios
rm -rf build
rm -rf Pods
rm -rf Podfile.lock

# Gerar novo projeto
echo "Gerando novo projeto..."
flutter clean
cd ..
flutter pub get
cd ios
pod install --repo-update

echo "=== Solução alternativa: alterar versão do deployment target ==="
echo "Você também pode tentar modificar manualmente o arquivo project.pbxproj para usar uma versão específica do iOS"
echo "1. Abra o Runner.xcworkspace no Xcode"
echo "2. Altere o 'iOS Deployment Target' para 15.0 em todas as configurações"
echo "3. Altere também o valor de 'MARKETING_VERSION' para 15.0 se necessário"

echo "Script concluído! Agora tente executar: flutter run -d ios" 