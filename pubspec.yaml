name: epraja
description: Aplicativo para busca de profissionais para serviços.
version: 1.1.0+27

environment:
  sdk: ">=2.17.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  http: ^1.2.2
  google_maps_flutter: ^2.9.0
  location: ^7.0.1
  shared_preferences: ^2.3.2
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  google_fonts: ^6.2.1
  geolocator: ^13.0.4
  url_launcher: ^6.3.1
  flux_validator_dart: ^2.0.0
  flutter_screenutil: ^5.9.3
  onesignal_flutter: ^5.0.4
  firebase_core: ^3.10.1
  flutter_secure_storage: ^9.2.2
  local_auth: ^2.1.8
  mask_text_input_formatter: ^2.9.0
  lottie: ^3.1.3
  package_info_plus: ^8.1.0
  image_picker: ^1.1.2
  image: ^4.3.0
  path: ^1.9.0
  path_provider: ^2.1.4
  mime: ^2.0.0
  http_parser: ^4.0.2
  google_mobile_ads: ^5.3.1
  geocoding: ^3.0.0
  intl: ^0.20.2
  device_info_plus: ^11.3.0
  font_awesome_flutter: ^10.8.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/logo.png
    - assets/logo_android12.png
    - assets/logocab.png
    - assets/logosplash.png
    - assets/user.png
    - assets/semDados.json

  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

dependency_overrides:
  js: ^0.6.7
  geolocator_android: 4.5.0  # Usando uma versão anterior mais estável