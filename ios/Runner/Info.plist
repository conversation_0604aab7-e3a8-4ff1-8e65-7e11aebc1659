<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>É pra Já</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>epraja</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>epraja</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>GADApplicationIdentifier</key>
	<string>ca-app-pub-3940256099942544~3986624511</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>whatsapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Este app precisa de acesso à câmera para capturar fotos</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Este app usa autenticação biométrica para maior segurança</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Este aplicativo precisa de acesso à localização para enviar sua posição ao cliente quando solicitado, mesmo em segundo plano.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs access to location when in the background to show nearby services.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to location when open to show nearby services.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Este app precisa de acesso ao microfone</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Este app precisa de acesso às suas fotos para permitir o upload de imagens</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchImageFile</key>
	<string></string>
	<key>UILaunchImageMinimumOSVersion</key>
	<string>15.0</string>
	<key>UILaunchImagePreserveAspectRatio</key>
	<true/>
	<key>UILaunchImageSize</key>
	<string>{1200, 1200}</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarColor</key>
	<string>#F6EEDD</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDarkContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>UIBackgroundColor</key>
	<string>#F6EEDD</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIViewEdgeAntialiasing</key>
	<true/>
	<key>GMSApiKey</key>
	<string>YOUR_GOOGLE_MAPS_API_KEY</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
</dict>
</plist>
