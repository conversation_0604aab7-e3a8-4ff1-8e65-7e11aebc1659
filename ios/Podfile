# Uncomment this line to define a global platform for your project
platform :ios, '15.0'

# Adicione esta linha no topo do arquivo, após a linha platform
source 'https://github.com/CocoaPods/Specs.git'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

# Configuração do deployment target
ENV['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  pod 'Firebase/Core'
  pod 'Google-Mobile-Ads-SDK'
  pod 'GoogleUtilities', '~> 8.0'

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  
  # Atualizando ambas as versões para serem compatíveis
  pod 'GoogleMaps', '8.4.0'
  pod 'Google-Maps-iOS-Utils', '5.0.0'

  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.build_configurations.each do |config|
    config.build_settings["SWIFT_VERSION"] = "5.0"
  end

  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.0'
      
      # Adicione estas configurações
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        'PERMISSION_LOCATION=1',
        'PERMISSION_NOTIFICATIONS=1',
        'PERMISSION_CAMERA=1',
        'PERMISSION_PHOTOS=1'
      ]
      
      # Configurações necessárias para o Xcode 16
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings['ENABLE_USER_SCRIPT_SANDBOXING'] = 'NO'
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'i386'
      config.build_settings['DEAD_CODE_STRIPPING'] = 'YES'
      config.build_settings['CODE_SIGN_IDENTITY'] = ''
      config.build_settings['SWIFT_VERSION'] = '5.0'
      config.build_settings['ONLY_ACTIVE_ARCH'] = 'YES'
      
      # Para resolver problemas de compilação com Google Maps
      if target.name == 'GoogleMaps' || target.name == 'Google-Maps-iOS-Utils'
        config.build_settings['CLANG_ENABLE_MODULES'] = 'YES'
        config.build_settings['DEFINES_MODULE'] = 'YES'
      end
    end
  end
end
