#!/bin/bash

echo "=== Correção específica para iPhone 11 com iOS 18.3.2 ==="

# Limpar completamente o Xcode
echo "Fechando o Xcode..."
killall Xcode 2>/dev/null || true
sleep 2

echo "Configurando para SDK específico..."
# Forçar o uso do SDK 17 em vez de 18
defaults write com.apple.dt.Xcode IDEiPhoneMaximumOSDeploymentTarget 17.7

# Criar diretórios de cache
mkdir -p ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex
mkdir -p ~/Library/Developer/Xcode/DerivedData/ModuleCache.noindex
mkdir -p ~/Library/Developer/Xcode/DerivedData

# Garantir permissões
chmod -R 755 ~/Library/Developer/Xcode/DerivedData

echo "Limpando projeto..."
cd ios
rm -rf build Pods Podfile.lock
flutter clean

echo "Reinstalando dependências e pods..."
cd ..
flutter pub get
cd ios
pod install --repo-update

# Editar config.xcodeproj para forçar SDK específico
echo "Agora vamos tentar compilar para uma versão de iOS específica (17.7)..."
echo "Abra o projeto no Xcode e altere as seguintes configurações:"
echo "1. No Xcode, clique no projeto 'Runner'"
echo "2. Selecione a aba 'Build Settings'"
echo "3. Defina 'iOS Deployment Target' para 15.0"
echo "4. Defina 'Enable User Script Sandboxing' para NO"
echo "5. Defina 'Enable App Sandbox' para NO (se esta opção estiver presente)"
echo "6. Clique na aba 'General' e defina iOS min version para 15.0"
echo "7. Tente compilar o projeto para seu dispositivo"

echo "Script concluído." 