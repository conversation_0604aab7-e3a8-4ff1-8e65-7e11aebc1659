#!/bin/bash

# Usar o xcodebuild para limpar os caches
echo "Limpando caches do Xcode usando xcodebuild..."
xcodebuild -alltargets clean
xcodebuild clean -workspace ios/Runner.xcworkspace -scheme Runner -configuration Debug
xcodebuild clean -workspace ios/Runner.xcworkspace -scheme Runner -configuration Release

# Limpar o projeto Flutter
echo "Limpando o projeto Flutter..."
flutter clean
flutter pub get

# Reinstalar os pods
echo "Reinstalando os pods..."
cd ios
pod deintegrate
rm -rf Pods
rm -rf Podfile.lock
pod install --repo-update

# Tentar limpar SDKStatCaches se possível
echo "Tentando limpar SDKStatCaches..."
rm -rf ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex 2>/dev/null || true

echo "Limpeza concluída!" 