# Checklist para Migração do Projeto para Xcode 16

## Preparação de Ambiente
- [x] Ativar Xcode 16 via `xcode-select`
- [x] Atualizar o Podfile para iOS 15.0+
- [x] Atualizar as configurações do Xcode no projeto
- [x] Limpar caches do Xcode

## Alterações Necessárias
- [x] Definir deployment target para iOS 15.0 em todos os lugares relevantes
- [x] Desativar o User Script Sandboxing (NO)
- [x] Ativar Dead Code Stripping (YES)
- [x] Atualizar AppDelegate.swift para compatibilidade com iOS 15+
- [x] Atualizar Info.plist para suporte ao iOS 15+

## Resolução de Problemas Comuns
- [x] Erro de SDKStatCache: Execute o script de limpeza de SDKStatCache
- [x] Erro de Session.modulevalidation: Execute o script de correção de módulos
- [x] Problemas de permissão: Abrir manualmente o Finder e excluir caches
- [x] Erros de limpeza: Limpar manualmente diretórios build antes de reinstalar pods

## Compilação no Xcode
1. Abra o projeto via `open ios/Runner.xcworkspace`
2. Verifique novamente o "Enable User Script Sandboxing" está definido como NO
3. Verifique se iOS Deployment Target está definido como 15.0
4. Certifique-se de que o Xcode 16 está selecionado (Xcode -> Settings -> Locations)
5. Selecione o dispositivo desejado
6. Execute a compilação

## Comandos Úteis
- Limpar projeto: `flutter clean`
- Obter dependências: `flutter pub get`
- Reinstalar pods: `cd ios && pod install --repo-update`
- Compilar para iOS: `flutter build ios --release`
- Limpar caches do Xcode: `rm -rf ~/Library/Developer/Xcode/DerivedData/*` 