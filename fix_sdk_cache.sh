#!/bin/bash

echo "Tentando corrigir problemas com SDKStatCaches..."

# Abrir o Finder para remover manualmente os caches
echo "Vamos abrir o Finder na pasta de SDKStatCaches para remoção manual (algumas pastas exigem permissões elevadas)."
echo "Por favor, remova qualquer pasta relacionada a 'iphoneos18.0' se ela existir."
open ~/Library/Developer/Xcode/DerivedData

# Criar pasta de SDKStatCaches se não existir
echo "Criando pasta SDKStatCaches (se necessário)..."
mkdir -p ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex

# Definir permissões
echo "Definindo permissões..."
chmod -R 755 ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex

echo "Tentando executar xcodebuild -quiet clean..."
cd ios && xcodebuild -quiet clean

echo "Script concluído. Agora tente executar 'flutter run' novamente." 