#!/bin/bash

echo "=== Solução para compilação com Xcode 16 para dispositivos iOS 18 ==="

# Fechar o Xcode
echo "Fechando o Xcode..."
killall Xcode 2>/dev/null || true
sleep 2

# Garantir que estamos usando o Xcode 16
echo "Ativando Xcode 16..."
sudo xcode-select -s /Applications/Xcode_16.app
xcodebuild -version

# Definir preferências específicas do Xcode
echo "Configurando preferências do Xcode..."
defaults write com.apple.dt.Xcode IDESkipPackagePluginFingerprintValidatation -bool YES
defaults write com.apple.dt.Xcode BuildSystemScheduleInherentlyParallelBuildCommands -bool NO
defaults write com.apple.dt.Xcode EnableBuildDebugging -bool YES

# Configurar para forçar o uso do iOS 17
echo "Configurando para forçar iOS 17..."
defaults write com.apple.dt.Xcode IDEiPhoneMaximumDeploymentTarget 17.7
defaults write com.apple.dt.Xcode IDEiOSMaximumSupportedOSVersion 17.7

# Criar pasta temporária
TEMP_DIR=$(mktemp -d)

# Criar diretório central do Xcode
echo "Criando diretório central..."
mkdir -p $TEMP_DIR/xcode_clean

# Limpar o cache do Xcode
echo "Limpando caches do Xcode..."
rm -rf ~/Library/Developer/Xcode/DerivedData/* 2>/dev/null || true
mkdir -p ~/Library/Developer/Xcode/DerivedData
mkdir -p ~/Library/Developer/Xcode/DerivedData/ModuleCache.noindex
mkdir -p ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex
chmod -R 755 ~/Library/Developer/Xcode/DerivedData

# Criar arquivo de módulo de validação
touch $TEMP_DIR/xcode_clean/Session.modulevalidation
cp $TEMP_DIR/xcode_clean/Session.modulevalidation ~/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/

# Limpar e preparar o projeto Flutter
echo "Limpando o projeto Flutter..."
cd "$(dirname "$0")"  # Navegar para o diretório do script
rm -rf ios/build ios/Pods ios/Podfile.lock
flutter clean
flutter pub get

# Modificar arquivos importantes
echo "Atualizando arquivos de configuração específicos..."

# Atualizar project.pbxproj
echo "Atualizando project.pbxproj..."
sed -i '' 's/SDKROOT = iphoneos;/SDKROOT = iphoneos17.0;/g' ios/Runner.xcodeproj/project.pbxproj

# Reinstalar os pods
echo "Reinstalando pods..."
cd ios
pod cache clean --all
pod deintegrate
pod install --repo-update

echo "===== Preparação concluída ====="
echo "Agora abra o projeto diretamente no Xcode:"
echo "  open Runner.xcworkspace"
echo ""
echo "No Xcode, siga estas etapas:"
echo "1. Selecione o projeto Runner no navegador"
echo "2. Em Build Settings, certifique-se de:"
echo "   - iOS Deployment Target = 15.0"
echo "   - Enable User Script Sandboxing = NO" 
echo "   - SDKROOT = iphoneos17.0 (digite manualmente se necessário)"
echo "3. Selecione seu dispositivo iOS e clique em executar"
echo ""
echo "IMPORTANTE: Se ainda houver problemas, compile diretamente pelo Xcode, não pelo Flutter CLI" 