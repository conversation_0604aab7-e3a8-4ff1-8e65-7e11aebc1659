#!/bin/bash

echo "Corrigindo problema de Session.modulevalidation..."

# Criar diretório ModuleCache.noindex se não existir
mkdir -p ~/Library/Developer/Xcode/DerivedData/ModuleCache.noindex

# Criar arquivo de sessão de validação vazio
touch ~/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation

# Definir permissões corretas
chmod 644 ~/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation
chmod 755 ~/Library/Developer/Xcode/DerivedData/ModuleCache.noindex

echo "Agora vamos limpar o build..."
cd ios
rm -rf build Pods Podfile.lock
rm -rf ~/Library/Developer/Xcode/DerivedData/*

echo "Reinstalando pods..."
pod install --repo-update

echo "Correção de Session.modulevalidation concluída!" 