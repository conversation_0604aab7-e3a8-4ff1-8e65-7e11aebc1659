allprojects {
    repositories {
        google()
        mavenCentral()
    }
    
    project.ext {
        compileSdkVersion = 35
        targetSdkVersion = 35
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
