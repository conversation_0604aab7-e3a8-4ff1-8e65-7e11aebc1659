<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="br.com.epraja.epraja">
    <uses-permission
        android:name="android.permission.USE_BIOMETRIC"/>
    <uses-permission
        android:name="android.permission.USE_FINGERPRINT"/>
    <uses-permission
        android:name="android.permission.INTERNET"/>
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission
        android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <!-- Para Android 8 e anteriores -->
    <uses-feature android:name="android.hardware.fingerprint" android:required="false"/>
    <application
        android:allowBackup="true"
        android:label="É pra Já"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:theme="@style/Theme.AppCompat.Light">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/Theme.AppCompat.Light"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"/>
            <meta-data
                android:name="io.flutter.embedding.android.BiometricTheme"
                android:resource="@style/BiometricTheme"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2"/>
        <!-- API key for Google Maps -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyBosl755cemIvnxLlt4THCheSvERLhrSbA"/>
        <!-- Adicione esta linha dentro da tag <application> -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-1608619618015017~6269286722"/>
    </application>
    <queries>
        <package
            android:name="com.whatsapp"/>
        <intent>
            <action
                android:name="android.intent.action.VIEW"/>
            <data
                android:scheme="https"/>
        </intent>
    </queries>
</manifest>