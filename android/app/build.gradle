plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

//android.buildTypes.release.ndk.debugSymbolLevel = SYMBOL_TABLE

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "br.com.epraja.epraja"
    compileSdk = 35
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "br.com.epraja.epraja"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        
        // Adicionar suporte explícito para biometria
        manifestPlaceholders += [
            'auth.BiometricLib.target.api': '28'  // Suporte até API 28 (Android 9)
        ]
    }

    signingConfigs {
        release {
            keyAlias = keystoreProperties['keyAlias']
            keyPassword = keystoreProperties['keyPassword']
            storeFile = keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword = keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.release
            ndk {
                debugSymbolLevel 'SYMBOL_TABLE'
            }
        }
    }

    buildFeatures {
        viewBinding true
    }
}

flutter {
    source = "../.."
}

dependencies {
    def appcompat_version = "1.2.0"
    implementation "androidx.appcompat:appcompat:$appcompat_version"
    implementation "androidx.biometric:biometric:1.0.1"
    implementation "androidx.core:core:1.6.0"
    implementation "androidx.fragment:fragment:1.2.5"
}
