enum Environment {
  dev,
  prod,
}

class EnvironmentConfig {
  static Environment _environment = Environment.dev; // Ambiente padrão

  static void setEnvironment(Environment env) {
    _environment = env;
  }

  static String get baseUrl {
    switch (_environment) {
      case Environment.dev:
        return 'https://tkinformidia.net/scriptcase/app/Epraja/api';
      case Environment.prod:
        return 'https://epraja.app.br/sistema/api';
    }
  }

  static bool get isDevelopment => _environment == Environment.dev;
  static bool get isProduction => _environment == Environment.prod;
}
