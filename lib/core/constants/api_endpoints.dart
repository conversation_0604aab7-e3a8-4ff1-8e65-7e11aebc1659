import '../config/environment.dart';

class ApiEndpoints {
  // URL base da API agora vem do EnvironmentConfig
  static String get baseUrl => EnvironmentConfig.baseUrl;

  // Autenticação e Manutenção de Usuário
  static String get login => '$baseUrl/api.php?funcao=login';
  static String get saveUserId => '$baseUrl/api.php?funcao=gravar_userid';
  static String get validateUserEmail =>
      '$baseUrl/api.php?funcao=validar_UserEmail';
  static String get validateToken => '$baseUrl/api.php?funcao=validar_Token';
  static String get saveNewPassword =>
      '$baseUrl/api.php?funcao=gravar_NovaSenha';
  static String get updateSubscriptionId =>
      '$baseUrl/api.php?funcao=update_subscription_id';
  static String get deleteUserId => '$baseUrl/api.php?funcao=excluiUsuario';

  // Cadastros
  static String get searchRegistration =>
      '$baseUrl/api.php?funcao=buscar_cadastro';
  static String get clientRegistration => '$baseUrl/api.php?funcao=cliente';
  static String get saveProfessional =>
      '$baseUrl/api.php?funcao=grava_profissional';
  static String get getProfessions =>
      '$baseUrl/api.php?funcao=buscar_profissoes';

  // Serviços
  static String get listServices => '$baseUrl/api.php?funcao=lista_servicos';
  static String get listProfessionalServices =>
      '$baseUrl/api.php?funcao=lista_servicos_profissional';
  static String get serviceReview => '$baseUrl/api.php?funcao=service_review';
  static String get updateServiceStatus =>
      '$baseUrl/api.php?funcao=update_status_servico';
  static String get registerRequest =>
      '$baseUrl/api.php?funcao=registrar_pedido_cliente';

  // Profissionais
  static String get searchProfessionals =>
      '$baseUrl/api.php?funcao=buscar_profissionais_online';
  static String get getProfessional =>
      '$baseUrl/api.php?funcao=buscar_profissional';
  static String get removeGPS =>
      '$baseUrl/api.php?funcao=excluiGPSProfissional';
  static String get saveProfessionalStatus =>
      '$baseUrl/api.php?funcao=grava_status_profissional';

  // Notificações
  static String getSubscriptionId =
      '$baseUrl/api.php?funcao=get_subscription_id';
  static String get sendNotification =>
      '$baseUrl/api.php?funcao=OneSignal_msg_cliente';

  // Pagamentos
  static String get searchProfessionalAccounts =>
      '$baseUrl/api.php?funcao=asaas_PesquisaContasProfissional';

  // Professional Register endpoints
  static Uri buscarCadastro() =>
      Uri.parse('$baseUrl/api.php?funcao=buscar_cadastro');

  static Uri gravaProfissional() =>
      Uri.parse('$baseUrl/api.php?funcao=grava_profissional');
}
