import 'package:epraja/screens/permission_check_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'screens/login_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'screens/home_screen_professional.dart';
import 'screens/home_screen_client.dart';
import 'screens/client_register_screen.dart';
import 'screens/professional_register_screen.dart';
import 'screens/search_professionals_screen.dart';
import 'screens/services.dart';
import 'screens/forgot_password.dart';
import 'screens/terms_conditions.dart';
import 'screens/privacy_policy.dart';
import './widgets/theme.dart';
import 'core/config/environment.dart';
import 'screens/guest_home_screen.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Definir o ambiente aqui - altere entre dev e prod conforme necessário
  EnvironmentConfig.setEnvironment(Environment.prod); // ou Environment.prod

  // Inicializar o OneSignal
  OneSignal.initialize(
      '************************************'); // Substitua pelo seu App ID do OneSignal

  // Ativar o debug do OneSignal em modo de desenvolvimento
  OneSignal.Debug.setLogLevel(OSLogLevel.verbose);

  // Solicitar permissão para notificações
  OneSignal.Notifications.requestPermission(true);

  // Configurar a UI do sistema de forma mais agressiva
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
    overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
  );

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final materialTheme = MaterialTheme(Theme.of(context).textTheme);

    return ScreenUtilInit(
      designSize: const Size(414, 896),
      builder: (context, child) => MaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'É pra Já',
        navigatorKey: navigatorKey,
        theme: materialTheme.light(), // Usando o tema light do MaterialTheme
        initialRoute: '/permission_check',
        routes: {
          '/login': (context) => const LoginScreen(),
          '/homeclient': (context) => const HomeScreenClient(),
          '/homeprofessional': (context) => const HomeScreenProfessional(),
          '/guest_home': (context) => const GuestHomeScreen(),
          '/client_register': (context) => const ClientRegisterScreen(),
          '/professional_register': (context) =>
              const ProfessionalRegisterScreen(),
          '/search_professionals': (context) =>
              const SearchProfessionalsScreen(professionId: 0),
          '/search_services': (context) => const ServiceListScreen(),
          '/forgot_password': (context) => const ForgotPasswordScreen(),
          '/terms_conditions': (context) => const TermsAndConditionsScreen(),
          '/privacy_policy': (context) => const PrivacyPolicyScreen(),
          '/permission_check': (context) => const PermissionCheckScreen(),
        },
      ),
    );
  }
}
