import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:epraja/core/constants/api_endpoints.dart';
import 'package:epraja/screens/privacy_policy.dart';
import 'package:epraja/screens/terms_conditions.dart';

class GuestHomeScreen extends StatefulWidget {
  const GuestHomeScreen({super.key});

  @override
  _GuestHomeScreenState createState() => _GuestHomeScreenState();
}

class _GuestHomeScreenState extends State<GuestHomeScreen> {
  List<Map<String, dynamic>> professions = [];
  List<Map<String, dynamic>> filteredProfessions = [];
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadProfessions();
    searchController.addListener(_filterProfessions);
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  Future<void> _loadProfessions() async {
    final response = await http.get(Uri.parse(ApiEndpoints.getProfessions));
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        setState(() {
          professions = (data['professions'] as List)
              .map((prof) => {
                    'id': prof['id'],
                    'profession': prof['profession'],
                    'logo': prof['logo'],
                  })
              .toList();
          filteredProfessions = professions;
        });
      }
    }
  }

  void _filterProfessions() {
    final query = searchController.text.toLowerCase();
    setState(() {
      filteredProfessions = professions.where((profession) {
        final professionName = profession['profession'].toLowerCase();
        return professionName.startsWith(query);
      }).toList();
    });
  }

  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Login Necessário'),
          content: Text(
              'Para contratar um serviço, você precisa fazer login ou criar uma conta.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushReplacementNamed(context, '/login');
              },
              child: Text('Fazer Login'),
            ),
          ],
        );
      },
    );
  }

  void _onProfessionSelected(int professionId) {
    _showLoginRequiredDialog();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(414, 896),
      builder: (context, child) {
        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            elevation: 0,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Explorar Profissionais',
                    style: TextStyle(fontSize: 20.sp)),
                Image.asset(
                  'assets/logo.png',
                  height: 70.h,
                ),
              ],
            ),
          ),
          drawer: Drawer(
            child: Column(
              children: <Widget>[
                UserAccountsDrawerHeader(
                  accountName: Text('Modo Convidado',
                      style: TextStyle(fontSize: 17.sp, color: Colors.black)),
                  accountEmail: Text(
                      'Faça login para acessar todos os recursos',
                      style: TextStyle(fontSize: 15.sp, color: Colors.black)),
                  currentAccountPicture: CircleAvatar(
                    backgroundColor: Colors.white,
                    child: ClipOval(
                      child: Image.asset('assets/user.png', fit: BoxFit.cover),
                    ),
                  ),
                ),
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    children: <Widget>[
                      ListTile(
                        leading: Icon(Icons.login, size: 24.sp),
                        title: Text('Fazer Login',
                            style: TextStyle(fontSize: 14.sp)),
                        onTap: () {
                          Navigator.pushReplacementNamed(context, '/login');
                        },
                      ),
                      ListTile(
                        leading: Icon(Icons.person_add, size: 24.sp),
                        title: Text('Cadastre-se como Cliente',
                            style: TextStyle(fontSize: 14.sp)),
                        onTap: () {
                          Navigator.pushNamed(context, '/client_register');
                        },
                      ),
                      ListTile(
                        leading: Icon(Icons.assignment),
                        title: Text('Termos e Condições de Uso',
                            style: TextStyle(fontSize: 14.sp)),
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      TermsAndConditionsScreen()));
                        },
                      ),
                      ListTile(
                        leading: Icon(Icons.description),
                        title: Text('Política de Privacidade',
                            style: TextStyle(fontSize: 14.sp)),
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => PrivacyPolicyScreen()));
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          body: SafeArea(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.all(16.w),
                  child: TextField(
                    controller: searchController,
                    decoration: const InputDecoration(
                      labelText: 'Digite uma profissão',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  height: 80,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white),
                    color: Colors.white,
                  ),
                  child: const Text('Espaço para Anúncio'),
                ),
                Expanded(
                  child: GridView.builder(
                    padding: EdgeInsets.all(16.w),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      childAspectRatio: 1,
                      mainAxisSpacing: 16.w,
                      crossAxisSpacing: 16.w,
                    ),
                    itemCount: filteredProfessions.length,
                    itemBuilder: (context, index) {
                      return ElevatedButton(
                        onPressed: () {
                          _onProfessionSelected(
                              filteredProfessions[index]['id']);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: EdgeInsets.zero,
                        ),
                        child: Center(
                          child: Image.memory(
                            base64Decode(filteredProfessions[index]['logo']),
                            height: 150.h,
                            width: 150.w,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
