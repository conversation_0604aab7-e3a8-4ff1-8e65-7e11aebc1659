import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lottie/lottie.dart';
import '../core/constants/api_endpoints.dart';

class ServiceListScreen extends StatefulWidget {
  const ServiceListScreen({super.key});

  @override
  _ServiceListScreenState createState() => _ServiceListScreenState();
}

class _ServiceListScreenState extends State<ServiceListScreen> {
  String? userId;
  List<Service> services = [];

  @override
  void initState() {
    super.initState();
    _fetchUserId();
  }

  Future<void> _fetchUserId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      userId = prefs.getString('userId');
    });
    if (userId != null) {
      _fetchServices();
    } else {
      // Handle case where userId is not available
    }
  }

  Future<void> _fetchServices() async {
    final url = Uri.parse(ApiEndpoints.listServices);
    final response = await http.post(
      url,
      body: {'userId': userId!},
    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        List<dynamic> serviceList = data['services'];
        setState(() {
          services = serviceList.map((e) => Service.fromJson(e)).toList();
        });
      } else {
        _showMessage('Erro ao registrar requisição: ${response.statusCode}');
      }
    } else {
      _showMessage('Erro de conexão: ${response.statusCode}');
    }
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Serviços',
          style: TextStyle(fontSize: 20.sp, color: Colors.black),
        ),
      ),
      body: Container(
        color: const Color.fromARGB(255, 246, 238, 221),
        child: SafeArea(
          child: CustomScrollView(
            slivers: <Widget>[
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (services.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Sem serviços contratados até o momento.',
                              style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 20.h),
                            Lottie.asset(
                              'assets/semDados.json', // Certifique-se de que o caminho do arquivo está correto
                              width: 200.w,
                              height: 200.h,
                            ),
                          ],
                        ),
                      );
                    } else {
                      return Card(
                        margin: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Requisição no. ${services[index].id}',
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                '${services[index].nome} - ${services[index].profissao}',
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8.h),
                              Row(
                                children: [
                                  Icon(Icons.info,
                                      color: Colors.grey, size: 20.sp),
                                  SizedBox(width: 8.h),
                                  Text(
                                    'Status do Serviço: ${services[index].status}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          fontSize: 14.sp,
                                        ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 8.h),
                              Row(
                                children: [
                                  Icon(Icons.calendar_today,
                                      color: Colors.grey, size: 20.sp),
                                  SizedBox(width: 8.h),
                                  Text(
                                    'Iniciado em: ${services[index].criadoEm}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          fontSize: 14.sp,
                                        ),
                                  ),
                                ],
                              ),
                              if (services[index].terminadoEm != null) ...[
                                SizedBox(height: 8.h),
                                Row(
                                  children: [
                                    Icon(Icons.calendar_today,
                                        color: Colors.grey, size: 20.sp),
                                    SizedBox(width: 8.h),
                                    Text(
                                      'Terminado em: ${services[index].terminadoEm}',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.copyWith(
                                            fontSize: 14.sp,
                                          ),
                                    ),
                                  ],
                                ),
                              ],
                              SizedBox(height: 16.h),
                              TextField(
                                controller: TextEditingController(
                                  text: services[index].comentarios ??
                                      'Nenhum comentário disponível',
                                ),
                                readOnly: true,
                                minLines: 1,
                                maxLines: null,
                                decoration: InputDecoration(
                                  labelText: 'Comentários',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                              SizedBox(height: 16.h),
                              ElevatedButton(
                                onPressed: services[index].dataComentarios ==
                                            null &&
                                        services[index].terminadoEm == null
                                    ? () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                ServiceRatingScreen(
                                                    service: services[index]),
                                          ),
                                        ).then((value) {
                                          _fetchServices();
                                        });
                                      }
                                    : null,
                                style: ElevatedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Container(
                                  width: double.infinity,
                                  alignment: Alignment.center,
                                  child: const Text('Avaliar Serviço'),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                  },
                  childCount: services.isEmpty ? 1 : services.length,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Service {
  final int id;
  final String nome;
  final String profissao;
  final String status;
  final String criadoEm;
  final String? terminadoEm;
  final String? comentarios;
  final String? dataComentarios;

  Service(
      {required this.id,
      required this.nome,
      required this.profissao,
      required this.status,
      required this.criadoEm,
      this.terminadoEm,
      this.comentarios,
      this.dataComentarios});

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'],
      nome: json['nome'],
      profissao: json['profissao'],
      status: json['status'],
      criadoEm: json['criado_em'],
      terminadoEm: json['terminado_em'],
      comentarios: json['comentarios'],
      dataComentarios: json['data_comentarios'],
    );
  }
}

class ServiceRatingScreen extends StatefulWidget {
  final Service service;

  const ServiceRatingScreen({super.key, required this.service});

  @override
  _ServiceRatingScreenState createState() => _ServiceRatingScreenState();
}

class _ServiceRatingScreenState extends State<ServiceRatingScreen> {
  int rating = 0;
  final TextEditingController _observationsController = TextEditingController();

  @override
  void dispose() {
    _observationsController.dispose();
    super.dispose();
  }

  Future<void> _submitData() async {
    final response = await http.post(
      Uri.parse(ApiEndpoints.serviceReview),
      body: {
        'service_request_id': widget.service.id.toString(),
        'rating': rating.toString(),
        'review': _observationsController.text,
      },
    );

    final data = json.decode(response.body);
    if (data['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Avaliação enviada com sucesso!'),
          duration: Duration(seconds: 3),
        ),
      );
      Navigator.pop(context);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao enviar avaliação: ${data['message']} '),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: const Text(
          'Avaliação do Serviço',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Requisição no. ${widget.service.id}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${widget.service.nome} - ${widget.service.profissao}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text('Status do Serviço: ${widget.service.status}'),
              const SizedBox(height: 8),
              Text('Iniciado em: ${widget.service.criadoEm}'),
              if (widget.service.terminadoEm != null) ...[
                const SizedBox(height: 8),
                Text('Terminado em: ${widget.service.terminadoEm}'),
              ],
              const SizedBox(height: 20),
              const Text('Avaliação:', style: TextStyle(fontSize: 18)),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    icon: Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      color: Colors.orange,
                    ),
                    onPressed: () {
                      setState(() {
                        rating = index + 1;
                      });
                    },
                  );
                }),
              ),
              const SizedBox(height: 20),
              const Text('Observações:', style: TextStyle(fontSize: 18)),
              const SizedBox(height: 10),
              TextField(
                controller: _observationsController,
                textInputAction: TextInputAction.done,
                maxLines: 5,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText:
                      'Digite suas observações sobre o serviço executado...',
                ),
              ),
              SizedBox(height: 20.h),
              ElevatedButton(
                onPressed: _submitData,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  child: const Text('Enviar Avaliação'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
