import 'dart:convert'; // Para decodificar a string Base64
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart'; // Para copiar para a área de transferência

class QRCodeScreen extends StatelessWidget {
  final String base64Image; // String Base64 da imagem do QR Code
  final String copyPasteCode; // Código para copiar e colar

  const QRCodeScreen({
    super.key,
    required this.base64Image,
    required this.copyPasteCode,
  });

  @override
  Widget build(BuildContext context) {
    // Decodifica a string Base64 para bytes
    final bytes = base64Decode(base64Image);

    return Scaffold(
      extendBodyBehindAppBar: true, // Adicionado para suporte edge-to-edge
      appBar: AppBar(
        elevation: 0,
        title: Text('QR Code', style: TextStyle(fontSize: 20.sp)),
      ),
      body: Container(
        color: const Color.fromARGB(255, 246, 238, 221),
        child: Safe<PERSON>rea(
          // Adicionado SafeArea para proteção do conteúdo
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Exibe a imagem do QR Code
                Image.memory(
                  bytes,
                  width: 200.w, // Largura da imagem
                  height: 200.h, // Altura da imagem
                ),
                SizedBox(height: 20.h),
                Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Text('Código para copiar e colar: $copyPasteCode'),
                ),
                SizedBox(height: 20.h),
                Padding(
                  padding: EdgeInsets.all(16.w),
                  child: ElevatedButton(
                    onPressed: () {
                      // Copiar o código para a área de transferência
                      Clipboard.setData(ClipboardData(text: copyPasteCode));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Código copiado!')),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                    ),
                    child: Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      child: Text(
                        'Copiar Código',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: 16.sp,
                            ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
