import 'package:epraja/screens/payment_details_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:epraja/core/constants/api_endpoints.dart';

class PaymentScreen extends StatefulWidget {
  final String? professionalId; // Recebe o ID do profissional ao chamar a tela

  const PaymentScreen({super.key, required this.professionalId});

  @override
  _PaymentScreenState createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  List<dynamic> invoices = [];
  bool isLoading = true;

  // Função para buscar cobranças da API ASAAS com o ID do profissional
  Future<void> fetchInvoices() async {
    final url = Uri.parse(ApiEndpoints.searchProfessionalAccounts);

    try {
      final response = await http.post(
        url,
        body: {'professionalId': widget.professionalId},
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'success') {
          setState(() {
            invoices = data['data'];
            isLoading = false;
          });
        } else {
          _showMessage('Erro ao buscar cobranças');
        }
      } else {
        _showMessage('Erro na requisição à API');
      }
    } catch (e) {
//      print(e);
      setState(() {
        isLoading = false;
      });
    }
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    fetchInvoices(); // Chama a função ao iniciar a tela
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Pagamentos', style: TextStyle(fontSize: 20.sp)),
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: invoices.length,
              itemBuilder: (context, index) {
                final invoice = invoices[index];
                return ListTile(
                  title: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: 'Vencimento: ',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontSize: 16.sp,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface, // Cor para ambos os temas
                                  ),
                        ),
                        TextSpan(
                          text: formatDate(invoice['data_vencimento']),
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontSize: 16.sp,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface, // Cor para ambos os temas
                                  ),
                        ),
                      ],
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Valor: ',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontSize: 16.sp,
                                  ),
                            ),
                            TextSpan(
                              text: invoice['valor'],
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontSize: 16.sp,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Status: ',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontSize: 16.sp,
                                  ),
                            ),
                            TextSpan(
                              text: formatStatus(invoice['status']),
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: getStatusColor(
                                    invoice['status']), // Cor baseada no status
                                fontSize: 16, // Tamanho do valor
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  isThreeLine: true,
                  onTap: () {
                    if (invoice['status'] == 'PENDING') {
                      String qrCodeData =
                          invoice['qrcode']; // Substitua pelo seu dado
                      String copyPasteCode =
                          invoice['payload']; // Substitua pelo seu dado
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => QRCodeScreen(
                            base64Image:
                                qrCodeData, // Supondo que você tenha esses dados
                            copyPasteCode:
                                copyPasteCode, // Supondo que você tenha esses dados
                          ),
                        ),
                      );
                    } else {
                      _showMessage('Pagamento já efetuado para esta parcela.');
                    }
                  },
                );
              },
            ),
    );
  }

// Função para formatar a data
  String formatDate(String date) {
    try {
      DateTime parsedDate =
          DateTime.parse(date); // Converte a string em DateTime
      return DateFormat('dd-MM-yyyy').format(parsedDate); // Formata a data
    } catch (e) {
      return date; // Retorna a data original em caso de erro
    }
  }

// Função para formatar o status
  String formatStatus(String status) {
    switch (status) {
      case 'PENDING':
        return 'PENDENTE';
      case 'RECEIVED':
      case 'RECEIVED_IN_CASH':
      case 'CONFIRMED':
        return 'PAGO';
      default:
        return 'DESCONHECIDO';
    }
  }

// Função para obter a cor do status
  Color getStatusColor(String status) {
    switch (status) {
      case 'PENDING':
        return Colors.red;
      case 'RECEIVED':
      case 'RECEIVED_IN_CASH':
      case 'CONFIRMED':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
