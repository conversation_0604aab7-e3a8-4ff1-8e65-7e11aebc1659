import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:async';

class PermissionCheckScreen extends StatefulWidget {
  const PermissionCheckScreen({super.key});

  @override
  State<PermissionCheckScreen> createState() => _PermissionCheckScreenState();
}

class _PermissionCheckScreenState extends State<PermissionCheckScreen> {
  late Future<bool> _locationCheck;
  StreamSubscription<ServiceStatus>? _serviceStatusSubscription;

  @override
  void initState() {
    super.initState();
    _locationCheck = _checkInitialLocationStatus();
    _setupLocationListener();
  }

  @override
  void dispose() {
    _serviceStatusSubscription?.cancel();
    super.dispose();
  }

  void _setupLocationListener() {
    _serviceStatusSubscription = Geolocator.getServiceStatusStream().listen(
      (ServiceStatus status) async {
        if (status == ServiceStatus.enabled) {
          // Quando o serviço é ativado, verifica a permissão
          LocationPermission permission = await Geolocator.checkPermission();

          if (permission == LocationPermission.always ||
              permission == LocationPermission.whileInUse) {
            // Se tudo estiver OK, navega para login
            if (mounted) {
              Navigator.pushReplacementNamed(context, '/login');
            }
          }
        }
      },
    );
  }

  Future<bool> _checkInitialLocationStatus() async {
    try {
      // Primeiro verifica se o serviço está ativado
      final bool serviceEnabled = await Geolocator.isLocationServiceEnabled();

      if (!serviceEnabled) {
        return false; // Precisa mostrar a tela
      }

      // Se chegou aqui, o serviço está ativado
      // Verifica a permissão
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        // Tenta solicitar a permissão
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return false; // Precisa mostrar a tela
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return false; // Precisa mostrar a tela
      }

      // Se chegou aqui, tudo está OK
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
      return true;
    } catch (e) {
      debugPrint('Erro ao verificar localização: $e');
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
      return true;
    }
  }

  Future<void> _openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();

      // Inicia verificações periódicas
      Timer.periodic(const Duration(seconds: 1), (timer) async {
        // Verifica o status do serviço
        final bool serviceEnabled = await Geolocator.isLocationServiceEnabled();

        if (serviceEnabled) {
          // Se o serviço estiver ativado, verifica a permissão
          LocationPermission permission = await Geolocator.checkPermission();

          if (permission == LocationPermission.always ||
              permission == LocationPermission.whileInUse) {
            // Cancela o timer
            timer.cancel();

            // Navega para login se tudo estiver OK
            if (mounted) {
              Navigator.pushReplacementNamed(context, '/login');
            }
            return;
          }
        }

        // Se após 10 segundos ainda não foi ativado, cancela o timer
        if (timer.tick >= 10) {
          timer.cancel();
        }
      });
    } catch (e) {
      debugPrint('Erro ao abrir configurações: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _locationCheck,
      builder: (context, snapshot) {
        // Enquanto verifica, mostra uma tela branca ou um loading sutil
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: SizedBox.shrink(), // Tela branca
            ),
          );
        }

        // Se precisar mostrar a tela de permissão
        if (snapshot.data == false) {
          return Scaffold(
            body: Container(
              color: const Color.fromARGB(255, 246, 238, 221),
              child: Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/logo.png',
                        height: 200.h,
                      ),
                      SizedBox(height: 32.h),
                      Icon(
                        Icons.location_off,
                        size: 64,
                        color: Colors.red,
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'Localização Desativada',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'Para usar o aplicativo, é necessário ativar a localização do seu dispositivo.',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 32.h),
                      ElevatedButton(
                        onPressed: () async {
                          await _openLocationSettings();
                        },
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                              horizontal: 32.w, vertical: 12.h),
                        ),
                        child: Text(
                          'Ativar Localização',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                      SizedBox(height: 16.h),
                      TextButton(
                        onPressed: () {
                          SystemNavigator.pop();
                        },
                        child: Text(
                          'Sair do Aplicativo',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }

        // Se chegou aqui, não precisa mostrar nada (já navegou para login)
        return const SizedBox.shrink();
      },
    );
  }
}
