import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:io';
import 'dart:convert';
import 'package:flux_validator_dart/flux_validator_dart.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
// ignore: library_prefixes
import 'package:path/path.dart' as Path;
import 'package:path_provider/path_provider.dart';
import 'package:mime/mime.dart';
import 'package:http_parser/http_parser.dart';
import 'package:epraja/core/constants/api_endpoints.dart';

class ProfessionalRegisterScreen extends StatefulWidget {
  const ProfessionalRegisterScreen({super.key});

  @override
  _ProfessionalRegisterScreenState createState() =>
      _ProfessionalRegisterScreenState();
}

class _ProfessionalRegisterScreenState
    extends State<ProfessionalRegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _fantasiaController = TextEditingController();
  final TextEditingController _cpfCnpjController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _loginController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _cepController = TextEditingController();
  final TextEditingController _enderecoController = TextEditingController();
  final TextEditingController _numeroController = TextEditingController();
  final TextEditingController _complementoController = TextEditingController();
  final TextEditingController _bairroController = TextEditingController();
  final TextEditingController _cidadeController = TextEditingController();
  final TextEditingController _ufController = TextEditingController();
  final TextEditingController _celularController = TextEditingController();

  // Variáveis para armazenar as imagens
  XFile? _personalPhoto;
  XFile? _documentPhoto;

  int? _selectedProfessionId;
  List<DropdownMenuItem<int>> _professionItems = [];

  bool isPessoaFisica = true; // Definir o valor padrão como Pessoa Física
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _isAgreed = false; // Variável para o checkbox

  // Máscaras para CPF e CNPJ
  var cpfFormatter = MaskTextInputFormatter(
    mask: '###.###.###-##',
    filter: {"#": RegExp(r'[0-9]')},
  );
  var cnpjFormatter = MaskTextInputFormatter(
    mask: '##.###.###/####-##',
    filter: {"#": RegExp(r'[0-9]')},
  );
  var cepFormatter = MaskTextInputFormatter(mask: '##.###-###');
  var celularFormatter = MaskTextInputFormatter(mask: '(##) #####-####');

  Future<void> _showImageSourceOptions(bool isPersonalPhoto) async {
    await showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.r)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Escolha uma opção',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20.h),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Tirar foto'),
                onTap: () {
                  Navigator.pop(context);
                  if (isPersonalPhoto) {
                    _pickPersonalPhoto(ImageSource.camera);
                  } else {
                    _pickDocumentPhoto(ImageSource.camera);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Escolher da galeria'),
                onTap: () {
                  Navigator.pop(context);
                  if (isPersonalPhoto) {
                    _pickPersonalPhoto(ImageSource.gallery);
                  } else {
                    _pickDocumentPhoto(ImageSource.gallery);
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickPersonalPhoto(ImageSource source) async {
    final pickedFile = await _picker.pickImage(
      source: source,
      imageQuality: 50,
    );

    if (pickedFile != null) {
      _personalPhoto = await _resizeImage(pickedFile.path, 154, 205);
      setState(() {});
    }
  }

  Future<void> _pickDocumentPhoto(ImageSource source) async {
    final pickedFile = await _picker.pickImage(
      source: source,
      imageQuality: 50,
    );

    if (pickedFile != null) {
      _documentPhoto = await _resizeImage(pickedFile.path, 200, 100);
      setState(() {});
    }
  }

  Future<XFile> _resizeImage(String filePath, int width, int height) async {
    // Carregar a imagem original
    final img.Image originalImage =
        img.decodeImage(await File(filePath).readAsBytes())!;

    // Redimension ar a imagem
    final img.Image resizedImage =
        img.copyResize(originalImage, width: width, height: height);

    // Salvar a imagem redimensionada em um arquivo temporário
    final Directory tempDir = await getTemporaryDirectory();
    final String resizedPath =
        '${tempDir.path}/resized_${Path.basename(filePath)}';
    File(resizedPath)
        .writeAsBytesSync(img.encodeJpg(resizedImage, quality: 85));

    return XFile(resizedPath);
  }

  @override
  void initState() {
    super.initState();
    _loadProfessions();
  }

  Future<void> _loadProfessions() async {
    try {
      final response = await http.get(Uri.parse(ApiEndpoints.getProfessions));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          setState(() {
            _professionItems = (data['professions'] as List)
                .map((profession) => DropdownMenuItem<int>(
                      value: profession['id'],
                      child: Text(profession['profession']),
                    ))
                .toList();
            if (_professionItems.isNotEmpty) {
              _selectedProfessionId = _professionItems.first.value;
            }
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _checkCPF() async {
    setState(() {
      _isLoading = true;
    });

    String cpfNumeros =
        _cpfCnpjController.text.replaceAll(RegExp(r'[^\d]'), '');

    final uri = ApiEndpoints.buscarCadastro();
    final response = await http.post(
      uri,
      body: {
        'cpf': cpfNumeros,
      },
    );

    setState(() {
      _isLoading = false;
    });

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        setState(() {
          _nameController.text = data['dados'][0]['nome'];
          _fantasiaController.text = data['dados'][0]['fantasia'];
          _emailController.text = data['dados'][0]['email'];
          _cepController.text = data['dados'][0]['cep'];
          _enderecoController.text = data['dados'][0]['endereco'];
          _numeroController.text = data['dados'][0]['numero'];
          _complementoController.text = data['dados'][0]['complemento'];
          _bairroController.text = data['dados'][0]['bairro'];
          _cidadeController.text = data['dados'][0]['cidade'];
          _ufController.text = data['dados'][0]['uf'];
          _celularController.text = data['dados'][0]['celular'];
        });
      } else {
        if (isPessoaFisica) {
          _showMessage('CPF não encontrado.');
        } else {
          _showMessage('CNPJ não encontrado.');
        }
        _clearControllers();
      }
    } else {
      _showMessage('Erro ao verificar CPF.');
    }
  }

  void _clearControllers() {
    _nameController.clear();
    _fantasiaController.clear();
    _emailController.clear();
    _celularController.clear();
    _cepController.clear();
    _enderecoController.clear();
    _numeroController.clear();
    _complementoController.clear();
    _bairroController.clear();
    _cidadeController.clear();
    _ufController.clear();
  }

  Future<void> _register() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_isAgreed) {
        _showMessage('Você precisa concordar com os Termos de Uso');
        return;
      }
      _formKey.currentState?.save();

      if (_passwordController.text != _confirmPasswordController.text) {
        _showMessage('As senhas não coincidem');
        return;
      }

      String cpfCnpjNumeros =
          _cpfCnpjController.text.replaceAll(RegExp(r'[^\d]'), '');
      String celularNumeros =
          _celularController.text.replaceAll(RegExp(r'[^\d]'), '');
      String cepNumeros = _cepController.text.replaceAll(RegExp(r'[^\d]'), '');

      if (_personalPhoto == null || _documentPhoto == null) {
        _showMessage('As fotos são obrigatórias.');
        return;
      }

      final uri = ApiEndpoints.gravaProfissional();
      final request = http.MultipartRequest('POST', uri);

      request.fields['name'] = _nameController.text;
      request.fields['fantasia'] = _fantasiaController.text;
      request.fields['cpf'] = cpfCnpjNumeros;
      request.fields['email'] = _emailController.text;
      request.fields['login'] = _loginController.text;
      request.fields['password'] = _passwordController.text;
      request.fields['cep'] = cepNumeros;
      request.fields['endereco'] = _enderecoController.text;
      request.fields['numero'] = _numeroController.text;
      request.fields['complemento'] = _complementoController.text;
      request.fields['bairro'] = _bairroController.text;
      request.fields['cidade'] = _cidadeController.text;
      request.fields['uf'] = _ufController.text;
      request.fields['celular'] = celularNumeros;
      request.fields['profession_id'] = _selectedProfessionId.toString();

      // Adicionar fotos, se disponíveis
      if (_personalPhoto != null) {
        String mimeType = lookupMimeType(_personalPhoto!.path) ?? 'image/jpeg';
        request.files.add(await http.MultipartFile.fromPath(
          'foto',
          _personalPhoto!.path,
          contentType: MediaType.parse(mimeType),
        ));
      }

      if (_documentPhoto != null) {
        String mimeType = lookupMimeType(_documentPhoto!.path) ?? 'image/jpeg';
        request.files.add(await http.MultipartFile.fromPath(
          'foto_documento',
          _documentPhoto!.path,
          contentType: MediaType.parse(mimeType),
        ));
      }

      // Enviar a requisição
      try {
        final response = await request.send();

        if (response.statusCode == 200) {
          final responseBody = await response.stream.bytesToString();
          final data = json.decode(responseBody);

          if (data['success']) {
            _showMessage(
                'Registro feito com sucesso! Os dados foram enviados para o seu email');
            Navigator.pushNamed(context, '/login');
          } else {
            _showMessage('Falha no registro: ${data['message']}');
          }
        } else {
          _showMessage('Erro na requisição: ${response.reasonPhrase}');
        }
      } catch (e) {
        _showMessage('Erro inesperado: $e');
      }
    }
  }

  Future<void> _fetchAddressByCEP(String cep) async {
    final uri = Uri.parse('https://viacep.com.br/ws/$cep/json/');
    final response = await http.get(uri);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['erro'] == null) {
        setState(() {
          _enderecoController.text = data['logradouro'] ?? '';
          _bairroController.text = data['bairro'] ?? '';
          _cidadeController.text = data['localidade'] ?? '';
          _ufController.text = data['uf'] ?? '';
        });
      } else {
        _showMessage('CEP não encontrado');
      }
    } else {
      _showMessage('Erro ao buscar CEP');
    }
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Cadastro de Profissional',
          style: TextStyle(fontSize: 20.sp, color: Colors.black),
        ),
      ),
      body: Container(
        color: const Color.fromARGB(255, 246, 238, 221),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitle('Informações de Usuário'),
                  _buildTextFormField(
                    controller: _loginController,
                    labelText: 'Usuário',
                    icon: Icons.verified_user,
                    onSaved: (value) => _loginController.text = value!,
                    keyboardType: TextInputType.text,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite seu usuário';
                      }
                      if (!RegExp(r'^[a-z0-9_]+$').hasMatch(value)) {
                        _showMessage(
                            'Usuário deve conter apenas letras minúsculas, números e deve ser uma palavra única');
                        return 'Erro no campo USUÁRIO';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 10.h),
                  _buildPasswordFormField(
                    controller: _passwordController,
                    labelText: 'Senha',
                    onSaved: (value) => _passwordController.text = value!,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite sua senha';
                      }
                      return null;
                    },
                    obscureText: _obscurePassword,
                    onTapIcon: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                  SizedBox(height: 10.h),
                  _buildPasswordFormField(
                    controller: _confirmPasswordController,
                    labelText: 'Confirme sua senha',
                    onSaved: (value) =>
                        _confirmPasswordController.text = value!,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, confirme sua senha';
                      }
                      return null;
                    },
                    obscureText: _obscureConfirmPassword,
                    onTapIcon: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                  ),
                  SizedBox(height: 20.h),
                  _buildSectionTitle('Informações de Cadastro'),
                  Row(
                    children: [
                      Radio(
                        value: true,
                        groupValue: isPessoaFisica,
                        onChanged: (value) {
                          setState(() {
                            isPessoaFisica = value as bool;
                          });
                        },
                      ),
                      const Text('Pessoa Física'),
                      Radio(
                        value: false,
                        groupValue: isPessoaFisica,
                        onChanged: (value) {
                          setState(() {
                            isPessoaFisica = value as bool;
                          });
                        },
                      ),
                      const Text('Pessoa Jurídica'),
                    ],
                  ),
                  SizedBox(height: 10.h),
                  Container(
                    width: double.infinity,
                    color: const Color(0xFFF0F1F0),
                    padding: EdgeInsets.symmetric(vertical: 8.0.h),
                    alignment: Alignment.center,
                    child: Text(
                      'As fotos abaixo são obrigatórias',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                  ),
                  SizedBox(height: 10.h),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            TextButton(
                              onPressed: () => _showImageSourceOptions(true),
                              child: Text(
                                _personalPhoto == null
                                    ? 'Foto pessoal'
                                    : 'Foto pessoal selecionada',
                              ),
                            ),
                            GestureDetector(
                                onTap: () => _showImageSourceOptions(true),
                                child: Container(
                                  width: 154.w,
                                  height: 205.h,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                  ),
                                  child: _personalPhoto == null
                                      ? Center(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(Icons.camera_alt,
                                                  size: 40, color: Colors.grey),
                                              SizedBox(height: 8),
                                              Text(
                                                'Adicionar foto',
                                                style: TextStyle(
                                                    color: Colors.grey),
                                              ),
                                            ],
                                          ),
                                        )
                                      : Image.file(
                                          File(_personalPhoto!.path),
                                          fit: BoxFit.cover,
                                        ),
                                )),
                          ],
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        child: Column(
                          children: [
                            TextButton(
                              onPressed: () => _showImageSourceOptions(false),
                              child: Text(
                                _documentPhoto == null
                                    ? 'Foto do Documento'
                                    : 'Documento selecionado',
                              ),
                            ),
                            GestureDetector(
                              onTap: () => _showImageSourceOptions(false),
                              child: Container(
                                height: 200.h,
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                ),
                                child: _documentPhoto == null
                                    ? Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(Icons.camera_alt,
                                                size: 40, color: Colors.grey),
                                            SizedBox(height: 8),
                                            Text(
                                              'Adicionar foto',
                                              style:
                                                  TextStyle(color: Colors.grey),
                                            ),
                                          ],
                                        ),
                                      )
                                    : Image.file(
                                        File(_documentPhoto!.path),
                                        fit: BoxFit.cover,
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _cpfCnpjController,
                    labelText: isPessoaFisica ? 'CPF' : 'CNPJ',
                    icon: Icons.credit_card,
                    onSaved: (value) => _cpfCnpjController.text = value!,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      isPessoaFisica ? cpfFormatter : cnpjFormatter
                    ],
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (isPessoaFisica) {
                        if (Validator.cpf(value) ||
                            value == null ||
                            value.isEmpty) {
                          return 'Por favor digite um CPF válido';
                        }
                      } else {
                        if (Validator.cnpj(value) ||
                            value == null ||
                            value.isEmpty) {
                          return 'Por favor digite um CNPJ válido';
                        }
                      }
                      return null;
                    },
                  ),
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : ElevatedButton(
                          onPressed: _checkCPF,
                          child: isPessoaFisica
                              ? const Text(
                                  'Verificar CPF',
                                )
                              : const Text(
                                  'Verificar CNPJ',
                                ),
                        ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _nameController,
                    labelText: isPessoaFisica ? 'Nome' : 'Razão Social',
                    icon: isPessoaFisica ? Icons.person : Icons.business,
                    onSaved: (value) => _nameController.text = value!,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite seu nome';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 10.h),
                  !isPessoaFisica
                      ? _buildTextFormField(
                          controller: _fantasiaController,
                          labelText: 'Nome Fantasia',
                          icon: Icons.business,
                          onSaved: (value) => _fantasiaController.text = value!,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Por favor, digite um nome fantasia';
                            }
                            return null;
                          },
                        )
                      : SizedBox(height: 0.h),
                  !isPessoaFisica
                      ? SizedBox(height: 10.h)
                      : SizedBox(height: 0.h),
                  _buildTextFormField(
                    controller: _emailController,
                    keyboardType: TextInputType.emailAddress,
                    labelText: 'Email',
                    icon: Icons.email,
                    onSaved: (value) => _emailController.text = value!,
                  ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _celularController,
                    labelText: 'Celular (com DDD)',
                    icon: Icons.phone,
                    onSaved: (value) => _celularController.text = value!,
                    inputFormatters: [celularFormatter],
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite seu celular';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 20.h),
                  _buildSectionTitle('Informações de Endereço'),
                  _buildTextFormField(
                    controller: _cepController,
                    labelText: 'CEP',
                    icon: Icons.location_on,
                    onSaved: (value) => _cepController.text = value!,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      cepFormatter
                    ],
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite seu CEP';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      // Verifica se o CEP tem 8 dígitos
                      if (value.replaceAll(RegExp(r'[^\d]'), '').length == 8) {
                        // Remove a formatação e chama a função com apenas números
                        String cepNumeros =
                            value.replaceAll(RegExp(r'[^\d]'), '');
                        _fetchAddressByCEP(cepNumeros);
                      }
                    },
                  ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _enderecoController,
                    labelText: 'Endereço',
                    icon: Icons.home,
                    onSaved: (value) => _enderecoController.text = value!,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite seu endereço';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _numeroController,
                    labelText: 'Digite o número',
                    icon: Icons.format_list_numbered,
                    onSaved: (value) => _numeroController.text = value!,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite o número';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _complementoController,
                    labelText: 'Digite o complemento',
                    icon: Icons.apartment,
                    onSaved: (value) => _complementoController.text = value!,
                  ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _bairroController,
                    labelText: 'Bairro',
                    icon: Icons.location_city,
                    onSaved: (value) => _bairroController.text = value!,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite seu bairro';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _cidadeController,
                    labelText: 'Cidade',
                    icon: Icons.location_city,
                    onSaved: (value) => _cidadeController.text = value!,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite sua cidade';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 10.h),
                  _buildTextFormField(
                    controller: _ufController,
                    labelText: 'UF',
                    icon: Icons.map,
                    onSaved: (value) => _ufController.text = value!,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Por favor, digite seu UF';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 10.h),
                  _professionItems.isNotEmpty
                      ? DropdownButtonFormField<int>(
                          value: _selectedProfessionId,
                          items: _professionItems,
                          onChanged: (value) {
                            setState(() {
                              _selectedProfessionId = value;
                            });
                          },
                          decoration: const InputDecoration(
                            labelText: 'Selecione uma profissão',
                            prefixIcon: Icon(Icons.work),
                          ),
                          validator: (value) {
                            if (value == null) {
                              return 'Por favor, selecione uma profissão';
                            }
                            return null;
                          },
                        )
                      : const Center(child: CircularProgressIndicator()),
                  SizedBox(height: 20.h),
                  Row(children: [
                    Checkbox(
                      value: _isAgreed,
                      onChanged: (value) {
                        setState(() {
                          _isAgreed = value!;
                        });
                      },
                    ),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          children: [
                            const TextSpan(
                              text: 'Declaro que li e concordo com os ',
                              style: TextStyle(color: Colors.black),
                            ),
                            TextSpan(
                              text: 'Termos de Uso',
                              style: const TextStyle(
                                color: Colors.blue,
                                decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  Navigator.pushNamed(
                                      context, '/terms_conditions');
                                },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ]),
                  SizedBox(height: 20.h),
                  ElevatedButton(
                    onPressed: _register,
                    child: Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      child: const Text(
                        'Registrar',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.0.h),
      child: Container(
        width: double.infinity,
        color: const Color(0xFFF0F1F0),
        alignment: Alignment.center,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordFormField({
    required String labelText,
    required FormFieldSetter<String> onSaved,
    required FormFieldValidator<String>? validator,
    TextEditingController? controller,
    required bool obscureText,
    required VoidCallback onTapIcon,
  }) {
    return TextFormField(
      onSaved: onSaved,
      validator: validator,
      obscureText: obscureText,
      controller: controller,
      decoration: InputDecoration(
        labelText: labelText,
        prefixIcon: const Icon(Icons.lock),
        suffixIcon: IconButton(
          icon: Icon(
            obscureText ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: onTapIcon,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0.r),
        ),
      ),
    );
  }

  Widget _buildTextFormField({
    required String labelText,
    required IconData icon,
    required FormFieldSetter<String> onSaved,
    FormFieldValidator<String>? validator,
    bool obscureText = false,
    TextEditingController? controller,
    ValueChanged<String>? onChanged,
    List<TextInputFormatter>? inputFormatters,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextFormField(
      onSaved: onSaved,
      validator: validator,
      obscureText: obscureText,
      controller: controller,
      onChanged: onChanged,
      inputFormatters: inputFormatters,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: labelText,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0.r),
        ),
      ),
    );
  }
}
