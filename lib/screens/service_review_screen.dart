import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/api_endpoints.dart';

class ServiceReviewScreen extends StatefulWidget {
  final int serviceRequestId;

  // ignore: use_key_in_widget_constructors
  const ServiceReviewScreen({required this.serviceRequestId});

  @override
  _ServiceReviewScreenState createState() => _ServiceReviewScreenState();
}

class _ServiceReviewScreenState extends State<ServiceReviewScreen> {
  int _rating = 0;
  final TextEditingController _reviewController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  Future<void> _submitReview() async {
    final response = await http.post(
      Uri.parse(ApiEndpoints.serviceReview),
      body: {
        'service_request_id': widget.serviceRequestId.toString(),
        'rating': _rating.toString(),
        'review': _reviewController.text,
      },
    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Avaliação enviada com sucesso!'),
            duration: Duration(seconds: 3),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao enviar avaliação: ${data['message']}'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro de rede: ${response.statusCode}'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Avaliação do Serviço',
          style: TextStyle(fontSize: 20.sp, color: Colors.black),
        ),
      ),
      body: Container(
        color: const Color.fromARGB(255, 246, 238, 221),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Text(
                    'Avaliação',
                    style: TextStyle(
                        fontSize:
                            18.sp), // Usando ScreenUtil para tamanho do texto
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(5, (index) {
                      return IconButton(
                        icon: Icon(
                          index < _rating ? Icons.star : Icons.star_border,
                          size:
                              30.sp, // Usando ScreenUtil para tamanho do ícone
                        ),
                        onPressed: () {
                          setState(() {
                            _rating = index + 1;
                          });
                        },
                      );
                    }),
                  ),
                  TextField(
                    controller: _reviewController,
                    decoration: InputDecoration(
                      labelText: 'Comentário',
                      labelStyle: TextStyle(
                          fontSize:
                              16.sp), // Usando ScreenUtil para tamanho do texto
                    ),
                    maxLines: 4,
                    minLines: 1,
                    style: TextStyle(
                        fontSize:
                            16.sp), // Usando ScreenUtil para tamanho do texto
                  ),
                  SizedBox(
                      height:
                          20.h), // Usando ScreenUtil para altura do SizedBox
                  ElevatedButton(
                    onPressed: _submitReview,
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      backgroundColor: const Color.fromRGBO(249, 163, 42, 1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                            12.r), // Usando ScreenUtil para bordas arredondadas
                      ),
                    ),
                    child: Text(
                      'Enviar Avaliação',
                      style: TextStyle(
                          fontSize:
                              16.sp), // Usando ScreenUtil para tamanho do texto
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
