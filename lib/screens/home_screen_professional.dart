import 'dart:async';
import 'package:epraja/screens/privacy_policy.dart';
import 'package:epraja/screens/professional_payment_screen.dart';
import 'package:epraja/screens/terms_conditions.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:lottie/lottie.dart';
import 'package:epraja/core/constants/api_endpoints.dart';
import '../core/config/environment.dart';
import 'dart:math';

class HomeScreenProfessional extends StatefulWidget {
  const HomeScreenProfessional({super.key});

  @override
  _HomeScreenProfessionalState createState() => _HomeScreenProfessionalState();
}

class _HomeScreenProfessionalState extends State<HomeScreenProfessional> {
  String _appVersion = 'Carregando...';
  String? userId;
  List<Service> services = [];
  bool isAvailable = true;
  String username = '';
  String profissao = '';
  Timer? _timer;
  int _currentMessageIndex = 0;
  final List<String> _encouragingMessages = [
    'Não se preocupe! Logo logo alguém estará precisando dos seus serviços.',
    'Aproveite para revisar seu perfil e garantir que está tudo atualizado!',
    'Os melhores profissionais são pacientes e persistentes.',
    'Fique tranquilo, sua oportunidade está chegando!',
  ];

  @override
  void initState() {
    super.initState();
    _fetchUserId();
    _initOneSignal();
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (mounted) {
        _fetchServices();
      }
    });
    _loadAppVersion();

    Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % _encouragingMessages.length;
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _loadAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String environment = EnvironmentConfig.isDevelopment ? 'Dev' : 'Prod';

    setState(() {
      _appVersion =
          'Versão ${packageInfo.version} (Build ${packageInfo.buildNumber}) - $environment';
    });
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _fetchUserId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        userId = prefs.getString('userId');
        username = prefs.getString('name')!; // Salvando o nome de usuário
        profissao = prefs.getString('profissao')!;
      });
    }
    if (userId != null) {
      _fetchServices();
    } else {
      // Handle case where userId is not available
    }
  }

  Future<void> _fetchServices() async {
    final url = Uri.parse(ApiEndpoints.listProfessionalServices);
    final response = await http.post(
      url,
      body: {'userId': userId!},
    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        List<dynamic> serviceList = data['services'];
        if (mounted) {
          setState(() {
            services = serviceList.map((e) => Service.fromJson(e)).toList();
          });
        }
      } else {
        _showMessage('Erro ao registrar requisição: ${response.statusCode}');
      }
    } else {
      _showMessage('Erro de conexão: ${response.statusCode}');
    }
  }

  Future<void> _logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('username');
    await prefs.remove('password');
    await prefs.remove('userId');
    await prefs.remove('profissao');
    await prefs.remove('name');
    Navigator.pushReplacementNamed(context, '/login');
  }

  void _initOneSignal() {
    OneSignal.Notifications.addClickListener((event) async {
      final Map<String, dynamic>? notificationData =
          event.notification.additionalData;

      String username = notificationData?['username'];
      Position position = await _determinePosition();

      await _sendLocationToServer(
          position.latitude, position.longitude, username);

      _showLocationSentDialog();
    });
  }

  Future<Position> _determinePosition() async {
    LocationPermission permission;

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.deniedForever) {
        return Future.error(
            'As permissões de localização estão permanentemente negadas.');
      }

      if (permission == LocationPermission.denied) {
        return Future.error('As permissões de localização foram negadas.');
      }
    }

    return await Geolocator.getCurrentPosition();
  }

  Future<void> _showLocationSentDialog() async {
    // Escolhe uma mensagem aleatória, excluindo a primeira
    final random = Random();
    final messages =
        _encouragingMessages.sublist(1); // Pula a primeira mensagem
    final randomMessage = messages[random.nextInt(messages.length)];

    await showDialog(
      context: context,
      barrierDismissible: false, // Impede fechar clicando fora
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.r),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.location_on,
                color: Colors.green,
                size: 50.sp,
              ),
              SizedBox(height: 16.h),
              Text(
                'Localização Enviada!',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              Text(
                'Sua localização foi enviada ao cliente com sucesso.',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16.h),
              Text(
                randomMessage,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context); // Fecha o dialog
                  Navigator.pushReplacementNamed(context, '/homeprofessional');
                },
                style: ElevatedButton.styleFrom(
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: Text(
                  'IR PARA MEUS SERVIÇOS',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _sendLocationToServer(
      double latitude, double longitude, String username) async {
    final url = Uri.parse(ApiEndpoints.saveProfessionalStatus);
    final response = await http.post(
      url,
      body: {
        'userId': userId,
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'username': username,
      },
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        _showLocationSentDialog();
      } else {
        _showMessage('Erro ao enviar localização: ${data['message']}');
      }
    } else {
      _showMessage('Erro de conexão: ${response.statusCode}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      // ignore: deprecated_member_use
      onPopInvoked: (bool didPop) async {
        if (didPop) return;

        final bool shouldPop = await _showExitConfirmationDialog(context);
        if (shouldPop && context.mounted) {
          Navigator.pushReplacementNamed(context, '/login');
        }
      },
      child: ScreenUtilInit(
        designSize: const Size(414, 896),
        builder: (context, child) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              elevation: 0,
              title: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Home Profissional', style: TextStyle(fontSize: 20.sp)),
                  Image.asset(
                    'assets/logo.png',
                    height: 70.h,
                  ),
                ],
              ),
            ),
            drawer: Drawer(
              child: Column(
                children: <Widget>[
                  UserAccountsDrawerHeader(
                    accountName: Text(username,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.black,
                        )),
                    accountEmail: Text(profissao,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.black,
                        )),
                    currentAccountPicture: CircleAvatar(
                      backgroundColor: Colors.white,
                      child: ClipOval(
                        child:
                            Image.asset('assets/user.png', fit: BoxFit.cover),
                      ),
                    ),
                  ),
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.zero,
                      children: <Widget>[
                        ListTile(
                          leading: Icon(Icons.person_add, size: 24.sp),
                          title: Text('Cadastre-se como Cliente',
                              style: TextStyle(fontSize: 14.sp)),
                          onTap: () {
                            Navigator.pushNamed(context, '/client_register');
                          },
                        ),
                        ListTile(
                          leading: Icon(Icons.payment),
                          title: Text('Pagamentos',
                              style: TextStyle(fontSize: 14.sp)),
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        PaymentScreen(professionalId: userId)));
                          },
                        ),
                        ListTile(
                          leading: Icon(Icons.assignment),
                          title: Text('Termos e Condições de Uso',
                              style: TextStyle(fontSize: 14.sp)),
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        TermsAndConditionsScreen()));
                          },
                        ),
                        ListTile(
                          leading: Icon(Icons.description),
                          title: Text('Política de Privacidade',
                              style: TextStyle(fontSize: 14.sp)),
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        PrivacyPolicyScreen()));
                          },
                        ),
                        ListTile(
                          leading:
                              Icon(Icons.delete_forever, color: Colors.red),
                          title: Text('Excluir Conta',
                              style: TextStyle(
                                  fontSize: 14.sp, color: Colors.red)),
                          onTap: () {
                            _showDeleteAccountDialog();
                          },
                        ),
                        ListTile(
                          leading: Icon(Icons.logout, size: 24.sp),
                          title:
                              Text('Sair', style: TextStyle(fontSize: 14.sp)),
                          onTap: _logout,
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      _appVersion,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            body: Container(
              color: const Color.fromARGB(255, 246, 238, 221),
              child: SafeArea(
                child: Column(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: double.infinity,
                      height: 100,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white),
                        color: Colors.white,
                      ),
                      child: const Text('Espaço para Anúncios'),
                    ),
                    Expanded(
                      child: services.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Ainda não chegou nenhum pedido?',
                                    style: TextStyle(
                                      fontSize: 20.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 12.h),
                                  Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 24.w),
                                    child: AnimatedSwitcher(
                                      duration:
                                          const Duration(milliseconds: 500),
                                      transitionBuilder: (Widget child,
                                          Animation<double> animation) {
                                        return FadeTransition(
                                          opacity: animation,
                                          child: SlideTransition(
                                            position: Tween<Offset>(
                                              begin: const Offset(0.0, 0.1),
                                              end: Offset.zero,
                                            ).animate(animation),
                                            child: child,
                                          ),
                                        );
                                      },
                                      child: Text(
                                        _encouragingMessages[
                                            _currentMessageIndex],
                                        key:
                                            ValueKey<int>(_currentMessageIndex),
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          color: Colors.grey[600],
                                          height: 1.5,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 20.h),
                                  Lottie.asset(
                                    'assets/semDados.json',
                                    width: 200.w,
                                    height: 200.h,
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              itemCount: services.length,
                              itemBuilder: (context, index) {
                                return Card(
                                  margin: EdgeInsets.symmetric(
                                      vertical: 8.h, horizontal: 16.w),
                                  elevation: 4,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12.r),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(16.w),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Requisição no: ${services[index].id}',
                                          style: TextStyle(
                                            fontSize: 18.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        SizedBox(height: 8.h),
                                        Text(
                                          'Cliente: ${services[index].nome}',
                                          style: TextStyle(
                                            fontSize: 18.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        SizedBox(height: 8.h),
                                        Text(
                                          'Iniciado em: ${services[index].criadoEm}',
                                          style: TextStyle(
                                            fontSize: 16.sp,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                        SizedBox(height: 8.h),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 12.w, vertical: 6.h),
                                          decoration: BoxDecoration(
                                            color: _getStatusColor(
                                                    services[index].status)
                                                .withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(8.r),
                                            border: Border.all(
                                              color: _getStatusColor(
                                                  services[index].status),
                                              width: 1,
                                            ),
                                          ),
                                          child: Text(
                                            'Status do Atendimento: ${_getStatusText(services[index].status)}',
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w600,
                                              color: _getStatusColor(
                                                  services[index].status),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Em Andamento':
        return Colors.red;
      case 'Finalizado':
        return Colors.green;
      default:
        return Colors.orange;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'Em Andamento':
        return 'Em Andamento';
      case 'Finalizado':
        return 'Finalizado';
      default:
        return 'Desconhecido';
    }
  }

  Future<bool> _showExitConfirmationDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) {
            return AlertDialog(
              title: Text('Sair'),
              content: Text('Tem certeza de que deseja sair?'),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text('Cancelar'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text('Sair'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Excluir Conta'),
          content:
              Text('Tem certeza de que quer que os seus dados sejam apagados?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Fecha o diálogo
              },
              child: Text('CANCELAR'),
            ),
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Fecha o diálogo
                _deleteAccount(); // Chama a função para excluir a conta
              },
              child: Text('SIM'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteAccount() async {
    try {
      final url = Uri.parse(ApiEndpoints.deleteUserId);
      final response = await http.post(
        url,
        body: {
          'username': username,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          _showMessage(data['message']);
          // Aguarda um pouco para mostrar a mensagem antes de fazer logout
          await Future.delayed(const Duration(seconds: 2));
          _logout(); // Faz logout após excluir a conta
        } else {
          _showMessage(data['message']);
        }
      } else {
        _showMessage('Erro ao excluir conta: ${response.statusCode}');
      }
    } catch (e) {
      _showMessage('Erro ao excluir conta: $e');
    }
  }
}

class Service {
  final int id;
  final String nome;
  final String status;
  final String criadoEm;
  final String? terminadoEm;

  Service({
    required this.id,
    required this.nome,
    required this.status,
    required this.criadoEm,
    this.terminadoEm,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'],
      nome: json['nome'],
      status: json['status'],
      criadoEm: json['criado_em'],
      terminadoEm: json['terminado_em'],
    );
  }
}

class ServiceRatingScreen extends StatefulWidget {
  final Service service;

  const ServiceRatingScreen({required this.service, super.key});

  @override
  _ServiceRatingScreenState createState() => _ServiceRatingScreenState();
}

class _ServiceRatingScreenState extends State<ServiceRatingScreen> {
  int rating = 0; // Initial rating value
  final TextEditingController _observationsController =
      TextEditingController(); // Controller for the observations

  @override
  void dispose() {
    _observationsController
        .dispose(); // Dispose the controller when the widget is disposed
    super.dispose();
  }

  Future<void> _submitData() async {
    final url = Uri.parse(ApiEndpoints.updateServiceStatus);
    final response = await http.post(
      url,
      body: {
        'service_request_id': widget.service.id.toString(),
        'review': _observationsController.text,
      },
    );

    final data = json.decode(response.body);
    if (data['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Serviço encerrado com sucesso!'),
          duration: Duration(seconds: 3),
        ),
      );
      Navigator.pop(context); // Voltar para a tela anterior após o sucesso
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao enviar avaliação: ${data['message']}'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(
          414, 896), // Tamanho do design base (exemplo: iPhone 11 Pro Max)
      builder: (context, child) {
        return Scaffold(
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: Colors.black,
                size: 24.sp, // Usando ScreenUtil para tamanhos de ícone
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            title: Text(
              'Encerramento do Serviço',
              style: TextStyle(
                fontSize: 20.sp, // Usando ScreenUtil para tamanhos de fonte
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          body: SingleChildScrollView(
            // Envolvendo o conteúdo em um SingleChildScrollView
            child: Padding(
              padding:
                  EdgeInsets.all(12.w), // Usando ScreenUtil para espaçamento
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Requisição no.: ${widget.service.id}',
                    style: TextStyle(
                      fontSize:
                          16.sp, // Usando ScreenUtil para tamanhos de fonte
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h), // Usando ScreenUtil para espaçamento
                  Text(
                    'Cliente: ${widget.service.nome}',
                    style: TextStyle(
                      fontSize:
                          16.sp, // Usando ScreenUtil para tamanhos de fonte
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h), // Usando ScreenUtil para espaçamento
                  Text('Status do Serviço: ${widget.service.status}',
                      style: TextStyle(fontSize: 16.sp)),
                  SizedBox(height: 8.h), // Usando ScreenUtil para espaçamento
                  Text('Iniciado em: ${widget.service.criadoEm}',
                      style: TextStyle(fontSize: 16.sp)),
                  if (widget.service.terminadoEm != null) ...[
                    SizedBox(height: 8.h), // Usando ScreenUtil para espaçamento
                    Text('Terminado em: ${widget.service.terminadoEm}',
                        style: TextStyle(fontSize: 16.sp)),
                  ],
                  SizedBox(height: 10.h), // Usando ScreenUtil para espaçamento
                  Text('Observações:', style: TextStyle(fontSize: 14.sp)),
                  SizedBox(height: 10.h), // Usando ScreenUtil para espaçamento
                  TextField(
                    controller: _observationsController,
                    style: TextStyle(fontSize: 16.sp),
                    maxLines: 4, // Definindo o número máximo de linhas
                    textInputAction:
                        TextInputAction.done, // Mostra o botão "OK" no teclado
                    decoration: InputDecoration(
                      hintText: 'Escreva suas observações aqui',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                  SizedBox(height: 20.h), // Usando ScreenUtil para espaçamento
                  Center(
                    child: ElevatedButton(
                      onPressed: _submitData,
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                            horizontal: 32.w, vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.r),
                        ),
                      ),
                      child: Container(
                        width: double.infinity,
                        alignment: Alignment.center,
                        child: Text(
                          'Encerrar Serviço',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
