import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flux_validator_dart/flux_validator_dart.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import '../core/constants/api_endpoints.dart';

class ClientRegisterScreen extends StatefulWidget {
  const ClientRegisterScreen({super.key});

  @override
  _ClientRegisterScreenState createState() => _ClientRegisterScreenState();
}

class _ClientRegisterScreenState extends State<ClientRegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _cpfController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _loginController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _cepController = TextEditingController();
  final TextEditingController _enderecoController = TextEditingController();
  final TextEditingController _numeroController = TextEditingController();
  final TextEditingController _complementoController = TextEditingController();
  final TextEditingController _bairroController = TextEditingController();
  final TextEditingController _cidadeController = TextEditingController();
  final TextEditingController _ufController = TextEditingController();
  final TextEditingController _celularController = TextEditingController();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _isAgreed = false; // Variável para o checkbox

  var cpfFormatter = MaskTextInputFormatter(
    mask: '###.###.###-##',
    filter: {"#": RegExp(r'[0-9]')},
  );
  var cepFormatter = MaskTextInputFormatter(mask: '##.###-###');
  var celularFormatter = MaskTextInputFormatter(mask: '(##) #####-####');

  // Função para verificar o CPF no backend
  Future<void> _checkCPF() async {
    setState(() {
      _isLoading = true;
    });

    // Remover os caracteres não numéricos do CPF
    String cpfNumeros = _cpfController.text.replaceAll(RegExp(r'[^\d]'), '');

    final uri = Uri.parse(ApiEndpoints.searchRegistration);
    final response = await http.post(
      uri,
      body: {
        'cpf': cpfNumeros,
      },
    );

    setState(() {
      _isLoading = false;
    });

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        // Preencher os campos com os dados retornados pela API
        setState(() {
          _nameController.text = data['dados'][0]['nome'];
          _emailController.text = data['dados'][0]['email'];
          _cepController.text = data['dados'][0]['cep'];
          _enderecoController.text = data['dados'][0]['endereco'];
          _numeroController.text = data['dados'][0]['numero'];
          _complementoController.text = data['dados'][0]['complemento'];
          _bairroController.text = data['dados'][0]['bairro'];
          _cidadeController.text = data['dados'][0]['cidade'];
          _ufController.text = data['dados'][0]['uf'];
          _celularController.text = data['dados'][0]['celular'];
        });
      } else {
        _showMessage('CPF não encontrado.');
        _clearControllers();
      }
    } else {
      _showMessage('Erro ao verificar CPF.');
    }
  }

  void _clearControllers() {
    _nameController.clear();
    _emailController.clear();
    _celularController.clear();
    _cepController.clear();
    _enderecoController.clear();
    _numeroController.clear();
    _complementoController.clear();
    _bairroController.clear();
    _cidadeController.clear();
    _ufController.clear();
  }

  Future<void> _register() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_isAgreed) {
        _showMessage('Você precisa concordar com os Termos de Uso');
        return;
      }
      _formKey.currentState?.save();

      if (_passwordController.text != _confirmPasswordController.text) {
        _showMessage('As senhas não coincidem');
        return;
      }

      // Remover os caracteres não numéricos de CPF, celular e CEP
      String cpfNumeros = _cpfController.text.replaceAll(RegExp(r'[^\d]'), '');
      String celularNumeros =
          _celularController.text.replaceAll(RegExp(r'[^\d]'), '');
      String cepNumeros = _cepController.text.replaceAll(RegExp(r'[^\d]'), '');

      final uri = Uri.parse(ApiEndpoints.clientRegistration);
      final response = await http.post(
        uri,
        body: {
          'name': _nameController.text,
          'cpf': cpfNumeros,
          'email': _emailController.text,
          'login': _loginController.text,
          'password': _passwordController.text,
          'cep': cepNumeros,
          'endereco': _enderecoController.text,
          'numero': _numeroController.text,
          'complemento': _complementoController.text,
          'bairro': _bairroController.text,
          'cidade': _cidadeController.text,
          'uf': _ufController.text,
          'celular': celularNumeros,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          _showMessage(
              'Registro feito com sucesso! Os dados foram enviados para o seu email');
          Navigator.pushNamed(context, '/login');
        } else {
          _showMessage('Falha no registro: ${data['message']}');
        }
      } else {
        _showMessage(response.body);
      }
    }
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _fetchAddressByCEP(String cep) async {
    final uri = Uri.parse('https://viacep.com.br/ws/$cep/json/');
    final response = await http.get(uri);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['erro'] == null) {
        setState(() {
          _enderecoController.text = data['logradouro'] ?? '';
          _bairroController.text = data['bairro'] ?? '';
          _cidadeController.text = data['localidade'] ?? '';
          _ufController.text = data['uf'] ?? '';
        });
      } else {
        _showMessage('CEP não encontrado');
      }
    } else {
      _showMessage('Erro ao buscar CEP');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(414, 896), // iPhone 11 Pro Max dimensions
      builder: (context, child) {
        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              'Cadastro do Cliente',
              style: TextStyle(fontSize: 20.sp, color: Colors.black),
            ),
          ),
          body: Container(
            color: const Color.fromARGB(255, 246, 238, 221),
            child: SafeArea(
              child: CustomScrollView(
                slivers: [
                  SliverPadding(
                    padding: EdgeInsets.all(16.0.w),
                    sliver: SliverList(
                      delegate: SliverChildListDelegate(
                        [
                          Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildSectionTitle('Informações de Usuário'),
                                _buildTextFormField(
                                  controller: _loginController,
                                  labelText: 'Usuário',
                                  icon: Icons.verified_user,
                                  onSaved: (value) =>
                                      _loginController.text = value!,
                                  keyboardType: TextInputType.text,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, Usuário';
                                    }
                                    if (!RegExp(r'^[a-z0-9_]+$')
                                        .hasMatch(value)) {
                                      _showMessage(
                                          'Usuário deve conter apenas letras minúsculas, números e deve ser uma palavra única');
                                      return 'Erro no campo USUÁRIO';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 10.h),
                                _buildPasswordFormField(
                                  controller: _passwordController,
                                  labelText: 'Senha',
                                  onSaved: (value) =>
                                      _passwordController.text = value!,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite sua senha';
                                    }
                                    return null;
                                  },
                                  obscureText: _obscurePassword,
                                  onTapIcon: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                                SizedBox(height: 10.h),
                                _buildPasswordFormField(
                                  controller: _confirmPasswordController,
                                  labelText: 'Confirme sua senha',
                                  onSaved: (value) =>
                                      _confirmPasswordController.text = value!,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, confirme sua senha';
                                    }
                                    return null;
                                  },
                                  obscureText: _obscureConfirmPassword,
                                  onTapIcon: () {
                                    setState(() {
                                      _obscureConfirmPassword =
                                          !_obscureConfirmPassword;
                                    });
                                  },
                                ),
                                SizedBox(height: 20.h),
                                _buildSectionTitle('Informações de Cadastro'),
                                _buildTextFormField(
                                  controller: _cpfController,
                                  labelText: 'CPF',
                                  icon: Icons.credit_card,
                                  onSaved: (value) =>
                                      _cpfController.text = value!,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    cpfFormatter
                                  ],
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    if (Validator.cpf(value) ||
                                        value == null ||
                                        value.isEmpty) {
                                      return 'Por favor digite um CPF válido';
                                    }
                                    return null;
                                  },
                                ),
                                _isLoading
                                    ? const Center(
                                        child: CircularProgressIndicator())
                                    : ElevatedButton(
                                        onPressed: _checkCPF,
                                        child: const Text(
                                          'Verificar CPF',
                                        ),
                                      ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _nameController,
                                  labelText: 'Nome',
                                  icon: Icons.person,
                                  onSaved: (value) =>
                                      _nameController.text = value!,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite seu nome';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _emailController,
                                  keyboardType: TextInputType.emailAddress,
                                  labelText: 'Email',
                                  icon: Icons.email,
                                  onSaved: (value) =>
                                      _emailController.text = value!,
                                ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _celularController,
                                  labelText: 'Celular (com DDD)',
                                  icon: Icons.phone,
                                  onSaved: (value) =>
                                      _celularController.text = value!,
                                  inputFormatters: [celularFormatter],
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite seu celular';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 20.h),
                                _buildSectionTitle('Informações de Endereço'),
                                _buildTextFormField(
                                  controller: _cepController,
                                  labelText: 'CEP',
                                  icon: Icons.location_on,
                                  onSaved: (value) =>
                                      _cepController.text = value!,
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly,
                                    cepFormatter
                                  ],
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite seu CEP';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    // Verifica se o CEP tem 8 dígitos
                                    if (value
                                            .replaceAll(RegExp(r'[^\d]'), '')
                                            .length ==
                                        8) {
                                      // Remove a formatação e chama a função com apenas números
                                      String cepNumeros = value.replaceAll(
                                          RegExp(r'[^\d]'), '');
                                      _fetchAddressByCEP(cepNumeros);
                                    }
                                  },
                                ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _enderecoController,
                                  labelText: 'Endereço',
                                  icon: Icons.home,
                                  onSaved: (value) =>
                                      _enderecoController.text = value!,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite seu endereço';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _numeroController,
                                  labelText: ' Digite o número',
                                  icon: Icons.format_list_numbered,
                                  onSaved: (value) =>
                                      _numeroController.text = value!,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite o número';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _complementoController,
                                  labelText: 'Digite o complemento',
                                  icon: Icons.apartment,
                                  onSaved: (value) =>
                                      _complementoController.text = value!,
                                ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _bairroController,
                                  labelText: 'Bairro',
                                  icon: Icons.location_city,
                                  onSaved: (value) =>
                                      _bairroController.text = value!,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite seu bairro';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _cidadeController,
                                  labelText: 'Cidade',
                                  icon: Icons.location_city,
                                  onSaved: (value) =>
                                      _cidadeController.text = value!,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite sua cidade';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 10.h),
                                _buildTextFormField(
                                  controller: _ufController,
                                  labelText: 'UF',
                                  icon: Icons.map,
                                  onSaved: (value) =>
                                      _ufController.text = value!,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Por favor, digite seu UF';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 20.h),
                                Row(children: [
                                  Checkbox(
                                    value: _isAgreed,
                                    onChanged: (value) {
                                      setState(() {
                                        _isAgreed = value!;
                                      });
                                    },
                                  ),
                                  Expanded(
                                    child: RichText(
                                      text: TextSpan(
                                        children: [
                                          const TextSpan(
                                            text:
                                                'Declaro que li e concordo com os ',
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                          TextSpan(
                                            text: 'Termos de Uso',
                                            style: const TextStyle(
                                              color: Colors.blue,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                Navigator.pushNamed(context,
                                                    '/terms_conditions');
                                              },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ]),
                                SizedBox(height: 20.h),
                                ElevatedButton(
                                  onPressed: _register,
                                  child: Container(
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    child: const Text(
                                      'Registrar',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.0.h),
      child: Container(
        width: double.infinity,
        color: const Color(0xFFF0F1F0),
        alignment: Alignment.center,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordFormField({
    required String labelText,
    required FormFieldSetter<String> onSaved,
    required FormFieldValidator<String>? validator,
    TextEditingController? controller,
    required bool obscureText,
    required VoidCallback onTapIcon,
  }) {
    return TextFormField(
      onSaved: onSaved,
      validator: validator,
      obscureText: obscureText,
      controller: controller,
      decoration: InputDecoration(
        labelText: labelText,
        prefixIcon: const Icon(Icons.lock),
        suffixIcon: IconButton(
          icon: Icon(
            obscureText ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: onTapIcon,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0.r),
        ),
      ),
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String labelText,
    required IconData icon,
    required FormFieldSetter<String> onSaved,
    FormFieldValidator<String>? validator,
    bool obscureText = false,
    ValueChanged<String>? onChanged,
    List<TextInputFormatter>? inputFormatters,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return TextFormField(
      onSaved: onSaved,
      validator: validator,
      obscureText: obscureText,
      controller: controller,
      onChanged: onChanged,
      inputFormatters: inputFormatters,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: labelText,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0.r),
        ),
      ),
    );
  }
}
