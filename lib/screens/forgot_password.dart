import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../core/constants/api_endpoints.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  _ForgotPasswordScreenState createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  String username = '';
  String email = '';
  String token = '';
  String newPassword = '';
  String confirmPassword = '';
  bool isTokenFieldVisible = false;
  bool isNewPasswordVisible = false;
  bool isPasswordFieldVisible = false;
  String errorMessage = '';
  Color errorMessageColor = Colors.red; // Definindo a cor da mensagem
  bool isPasswordHidden = true;
  bool isConfirmPasswordHidden = true;
  final FocusNode tokenFocusNode = FocusNode();
  final FocusNode newPasswordFocusNode = FocusNode();
  final FocusNode confirmPasswordFocusNode = FocusNode();
  final ScrollController scrollController = ScrollController();
  final _formKey = GlobalKey<FormState>();

  Future<bool> validateAndSendToken(String username, String email) async {
    bool retorno = false;
    var url = Uri.parse(ApiEndpoints.validateUserEmail);
    try {
      var response =
          await http.post(url, body: {'username': username, 'email': email});
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'success') {
          retorno = true;
        }
      }
    } catch (e) {
      retorno = false;
    }
    return retorno;
  }

  Future<bool> validateToken(String username, String token) async {
    bool retorno = false;
    var url = Uri.parse(ApiEndpoints.validateToken);
    try {
      var response =
          await http.post(url, body: {'username': username, 'token': token});
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'success') {
          retorno = true;
        }
      }
    } catch (e) {
      retorno = false;
    }
    return retorno;
  }

  Future<String> saveNewPassword(String username, String newPassword) async {
    String retorno = '';
    var url = Uri.parse(ApiEndpoints.saveNewPassword);
    try {
      var response = await http
          .post(url, body: {'username': username, 'newPassword': newPassword});
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'success') {
          retorno = 'OK';
        } else {
          retorno = data['message'] ?? 'Erro ao salvar a nova senha';
        }
      }
    } catch (e) {
      retorno = 'Erro ao acessar o servidor';
    }
    return retorno;
  }

  void _scrollToVisible() {
    Future.delayed(Duration(milliseconds: 100), () {
      scrollController.animateTo(
        scrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  bool isPasswordStrong(String password) {
    if (password.length < 8) return false;
    if (!RegExp(r'[A-Z]').hasMatch(password)) return false;
    if (!RegExp(r'[a-z]').hasMatch(password)) return false;
    if (!RegExp(r'[0-9]').hasMatch(password)) return false;
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) return false;

    List<String> commonPasswords = [
      "123456",
      "password",
      "123456789",
      "12345678",
      "12345",
      "1234567",
      "qwerty",
      "abc123",
      "senha",
    ];

    return !commonPasswords.contains(password);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Recuperar Senha',
          style: TextStyle(fontSize: 20.sp, color: Colors.black),
        ),
      ),
      body: Container(
        color: const Color.fromARGB(255, 246, 238, 221),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            controller: scrollController,
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  TextField(
                    onChanged: (value) => username = value,
                    decoration: InputDecoration(labelText: "Usuário"),
                  ),
                  SizedBox(height: 10),
                  TextField(
                    onChanged: (value) => email = value,
                    keyboardType: TextInputType.emailAddress,
                    decoration: InputDecoration(labelText: "E-mail"),
                  ),
                  if (isTokenFieldVisible) ...[
                    SizedBox(height: 10),
                    TextField(
                      focusNode: tokenFocusNode, // Adicionado focusNode
                      onChanged: (value) => token = value,
                      decoration: InputDecoration(labelText: 'Token'),
                    ),
                  ],
                  if (isPasswordFieldVisible) ...[
                    SizedBox(height: 10),
                    TextField(
                      focusNode: newPasswordFocusNode, // Adicionado focusNode
                      obscureText: isPasswordHidden,
                      onChanged: (value) => newPassword = value,
                      decoration: InputDecoration(
                        labelText: 'Nova Senha',
                        suffixIcon: IconButton(
                          icon: Icon(
                            isPasswordHidden
                                ? Icons.visibility_off
                                : Icons.visibility,
                          ),
                          onPressed: () {
                            setState(() {
                              isPasswordHidden = !isPasswordHidden;
                            });
                          },
                        ),
                      ),
                    ),
                    SizedBox(height: 10),
                    TextField(
                      focusNode:
                          confirmPasswordFocusNode, // Adicionado focusNode
                      obscureText: isConfirmPasswordHidden,
                      onChanged: (value) => confirmPassword = value,
                      decoration: InputDecoration(
                        labelText: 'Confirmar Senha',
                        suffixIcon: IconButton(
                          icon: Icon(
                            isConfirmPasswordHidden
                                ? Icons.visibility_off
                                : Icons.visibility,
                          ),
                          onPressed: () {
                            setState(() {
                              isConfirmPasswordHidden =
                                  !isConfirmPasswordHidden;
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                  if (errorMessage.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: Text(
                        errorMessage,
                        style: TextStyle(color: errorMessageColor),
                      ),
                    ),
                  SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () async {
                      if (!isTokenFieldVisible) {
                        bool isEmailValid =
                            await validateAndSendToken(username, email);
                        setState(() {
                          if (isEmailValid) {
                            isTokenFieldVisible = true;
                            errorMessage = '';
                            _scrollToVisible();
                            FocusScope.of(context).requestFocus(
                                tokenFocusNode); // Foco no campo Token
                          } else {
                            errorMessage = "E-mail não cadastrado";
                            _scrollToVisible();
                          }
                        });
                      } else if (isTokenFieldVisible &&
                          !isPasswordFieldVisible) {
                        bool isTokenValid =
                            await validateToken(username, token);
                        setState(() {
                          if (isTokenValid) {
                            isPasswordFieldVisible = true;
                            errorMessage = '';
                            _scrollToVisible();
                            FocusScope.of(context).requestFocus(
                                newPasswordFocusNode); // Foco no campo Nova Senha
                          } else {
                            errorMessage = "Token inválido";
                            _scrollToVisible();
                          }
                        });
                      } else {
                        if (isPasswordStrong(newPassword)) {
                          if (newPassword == confirmPassword) {
                            String result =
                                await saveNewPassword(username, newPassword);
                            if (result == 'OK') {
                              setState(() {
                                errorMessage = "Senha redefinida com sucesso!";
                                errorMessageColor = Colors
                                    .green; // Mensagem de sucesso em verde
                              });
                            } else {
                              setState(() {
                                errorMessage = result;
                                errorMessageColor =
                                    Colors.red; // Mensagem de erro em vermelho
                              });
                            }
                          } else {
                            setState(() {
                              errorMessage = "As senhas não são iguais.";
                              errorMessageColor = Colors.red;
                              FocusScope.of(context).requestFocus(
                                  confirmPasswordFocusNode); // Foco no campo Confirmar Senha
                            });
                          }
                        } else {
                          setState(() {
                            errorMessage =
                                "A nova senha não atende aos critérios de segurança.";
                            errorMessageColor = Colors.red;
                          });
                        }
                      }
                    },
                    child: Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      child: Text(
                        !isTokenFieldVisible
                            ? "Enviar"
                            : !isPasswordFieldVisible
                                ? "Validar Token"
                                : "Salvar Senha",
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
