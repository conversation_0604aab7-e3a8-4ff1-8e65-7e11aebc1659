import 'dart:io';
import 'package:epraja/screens/forgot_password.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import '../core/constants/api_endpoints.dart';
import '../services/secure_storage_service.dart';
import 'package:package_info_plus/package_info_plus.dart';

// Movendo a classe SavedUser para o nível superior
class SavedUser {
  final String username;
  final String userType;

  SavedUser(this.username, this.userType);

  String get typeDescription => userType == 'C' ? 'Cliente' : 'Profissional';
}

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _loginController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberMe = false;
  bool _isPasswordVisible = false;
  final _secureStorage = const FlutterSecureStorage();
  final LocalAuthentication auth = LocalAuthentication();
  List<SavedUser> _savedUsers = [];
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _migrateData();
    _loadSavedUsers();
    _checkBiometricStatus();
    _loadAppVersion();
    // Configura a barra de status de acordo com a plataforma
    if (Platform.isAndroid) {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Color.fromARGB(
              255, 246, 238, 221), // Cor da barra de status no Android
          statusBarIconBrightness: Brightness.dark, // Ícones escuros no Android
        ),
      );
    } else if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent, // Cor da barra de status no iOS
          statusBarIconBrightness: Brightness.light, // Ícones claros no iOS
          statusBarBrightness:
              Brightness.light, // Para iOS, define o brilho da barra de status
        ),
      );
    }
  }

  Future<void> _migrateData() async {
    final prefs = await SharedPreferences.getInstance();
    bool isMigrated = prefs.getBool('isMigrated') ?? false;

    if (!isMigrated) {
      // Migrando dados
      String? username = prefs.getString('username');
      String? password = prefs.getString('password');
      await _secureStorage.write(key: 'username', value: username);
      await _secureStorage.write(key: 'password', value: password);

      // Atualiza o estado para marcar a migração como concluída
      await prefs.setBool('isMigrated', true);
    }
  }

  Future<void> _loadSavedUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersList = prefs.getStringList('saved_users') ?? [];

      List<SavedUser> validUsers = [];
      for (String username in usersList) {
        final password = await _secureStorage.read(key: 'password_$username');
        final userType = await _secureStorage.read(key: 'usertype_$username');

        if (password != null && userType != null) {
          validUsers.add(SavedUser(username, userType));
        }
      }

      setState(() {
        _savedUsers = validUsers;
      });
    } catch (e) {
      debugPrint('Erro ao carregar usuários: $e');
    }
  }

  Future<void> _saveData(String userId, String username, String profissao,
      String name, String cidade, String uf, String userType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('rememberMe', _rememberMe);
      await prefs.setString('userId', userId);
      await prefs.setString('username', username);
      await prefs.setString('name', name);
      await prefs.setString('uf', uf);

      if (profissao.isNotEmpty) {
        await prefs.setString('profissao', profissao);
      }

      if (_rememberMe) {
        List<String> savedUsers = prefs.getStringList('saved_users') ?? [];

        if (!savedUsers.contains(username)) {
          savedUsers.add(username);
          await prefs.setStringList('saved_users', savedUsers);
        }

        await _secureStorage.write(
            key: 'password_$username', value: _passwordController.text);
        await _secureStorage.write(key: 'usertype_$username', value: userType);
      }
    } catch (e) {
      debugPrint('Erro ao salvar dados: $e');
    }
  }

  Future<void> _authenticateBiometrics(String username) async {
    try {
      final auth = LocalAuthentication();
      bool isAuthenticated = false;

      if (Platform.isAndroid) {
        // Configuração simples que funcionou anteriormente
        isAuthenticated = await auth.authenticate(
          localizedReason: 'Por favor, autentique para continuar',
          options: const AuthenticationOptions(
            useErrorDialogs: false,
            stickyAuth: false,
            biometricOnly: false,
          ),
        );
      } else {
        isAuthenticated = await auth.authenticate(
          localizedReason: 'Por favor, autentique para continuar',
          options: const AuthenticationOptions(
            useErrorDialogs: true,
            stickyAuth: true,
          ),
        );
      }

      if (isAuthenticated && mounted) {
        final password = await _secureStorage.read(key: 'password_$username');
        if (password != null) {
          setState(() {
            _loginController.text = username;
            _passwordController.text = password;
          });
          await _login();
        }
      }
    } catch (e) {
      debugPrint('Erro: $e');
      if (mounted) {
        _showMessage(context, 'Não foi possível realizar a autenticação');
      }
    }
  }

  Future<void> _logoutBiometrics(String username) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> savedUsers = prefs.getStringList('saved_users') ?? [];
    savedUsers.remove(username);
    await prefs.setStringList('saved_users', savedUsers);

    await _secureStorage.delete(key: 'password_$username');

    setState(() {
      _savedUsers.removeWhere((user) => user.username == username);
    });
  }

  Future<void> _login() async {
    final cleanedUsername = _loginController.text
        .trim()
        .replaceAll(RegExp(r'\s+'), '')
        .toLowerCase();

    final password = _passwordController.text.trim();

    if (cleanedUsername.isEmpty || password.isEmpty) {
      _showMessage(context, 'Por favor, preencha todos os campos');
      return;
    }

    try {
      final uri = Uri.parse(ApiEndpoints.login);

      final response = await http.post(
        uri,
        body: {
          'login': cleanedUsername,
          'password': password,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          String profissao = data['profissao'] ?? '';

          // Salvar dados no SecureStorage
          await SecureStorageService.saveUserId(data['id']);
          await SecureStorageService.saveUserType(data['user_type']);
          await SecureStorageService.saveBiometricEnabled(_rememberMe);

          // Salvar outros dados no SharedPreferences como antes
          await _saveData(data['id'], cleanedUsername, profissao, data['nome'],
              data['cidade'], data['uf'], data['user_type']);

          SharedPreferences prefs = await SharedPreferences.getInstance();
          bool isFirstLogin = prefs.getBool('isFirstLogin') ?? true;

          if (isFirstLogin) {
            OneSignal.Notifications.requestPermission(true);
            prefs.setBool('isFirstLogin', false);
          }

          // Nova lógica para gerenciar o subscription_id
          await _handleOneSignalSubscription(data['id']);

          if (data['user_type'] == 'C') {
            Navigator.pushReplacementNamed(context, '/homeclient');
          } else {
            Navigator.pushReplacementNamed(context, '/homeprofessional');
          }
        } else {
          _showMessage(context,
              'Usuário ou senha incorretos. Por favor, verifique suas credenciais.');
        }
      } else {
        debugPrint('response: $response');
        _showMessage(context, 'Erro ao fazer login: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Erro ao fazer login: $e');
      _showMessage(context, 'Erro ao fazer login: $e');
    }
  }

  Future<void> _handleOneSignalSubscription(String userId) async {
    try {
      debugPrint('Buscando subscription_id para o usuário: $userId');

      // Primeiro, garantir que temos permissão do OneSignal
      bool hasPermission = OneSignal.Notifications.permission;
      if (!hasPermission) {
        debugPrint('Solicitando permissão do OneSignal');
        hasPermission = await OneSignal.Notifications.requestPermission(true);
      }

      // Verificar o status atual do OneSignal
      debugPrint('Status atual do OneSignal:');
      debugPrint('Permission: $hasPermission');
      debugPrint(
          'Current subscription ID: ${OneSignal.User.pushSubscription.id}');
      debugPrint('Push enabled: ${OneSignal.User.pushSubscription.optedIn}');

      final subscriptionResponse = await http.get(
        Uri.parse('${ApiEndpoints.getSubscriptionId}&user_id=$userId'),
      );

      debugPrint('Response status: ${subscriptionResponse.statusCode}');
      debugPrint('Response body: ${subscriptionResponse.body}');

      if (subscriptionResponse.statusCode == 200) {
        if (subscriptionResponse.body.isEmpty) {
          debugPrint('Resposta vazia do servidor');
          throw Exception('Resposta vazia do servidor');
        }

        final subscriptionData = json.decode(subscriptionResponse.body);
        String? existingSubscriptionId = subscriptionData['subscription_id'];
        bool needsNewSubscription = true;

        // Verificar se existe um subscription_id válido
        if (existingSubscriptionId != null &&
            existingSubscriptionId.isNotEmpty) {
          String? currentSubscriptionId = OneSignal.User.pushSubscription.id;
          debugPrint('Current subscription ID: $currentSubscriptionId');
          debugPrint('Existing subscription ID: $existingSubscriptionId');

          if (currentSubscriptionId == existingSubscriptionId) {
            needsNewSubscription = false;
            debugPrint('Usando subscription_id existente');
          }
        }

        // Se precisar de novo subscription_id
        if (needsNewSubscription) {
          debugPrint('Aguardando OneSignal inicializar...');
          // Aguardar um momento para garantir que o OneSignal está pronto
          await Future.delayed(const Duration(seconds: 2));

          String? newSubscriptionId = OneSignal.User.pushSubscription.id;
          debugPrint('Novo subscription ID obtido: $newSubscriptionId');

          if (newSubscriptionId != null && newSubscriptionId.isNotEmpty) {
            debugPrint('Atualizando subscription_id no backend');
            await _sendUserDataToBackend(userId, newSubscriptionId);
          } else {
            debugPrint('Tentando obter subscription_id novamente...');
            // Tentar mais uma vez após um breve delay
            await Future.delayed(const Duration(seconds: 2));
            newSubscriptionId = OneSignal.User.pushSubscription.id;

            if (newSubscriptionId != null && newSubscriptionId.isNotEmpty) {
              await _sendUserDataToBackend(userId, newSubscriptionId);
            } else {
              debugPrint(
                  'Não foi possível obter subscription_id do OneSignal após múltiplas tentativas');
            }
          }
        }
      } else {
        throw Exception(
            'Erro na requisição: ${subscriptionResponse.statusCode}');
      }
    } catch (e) {
      debugPrint('Erro ao gerenciar subscription do OneSignal: $e');
    }
  }

  Future<void> _sendUserDataToBackend(
      String userId, String subscriptionId) async {
    try {
      debugPrint('Enviando dados para o backend:');
      debugPrint('User ID: $userId');
      debugPrint('Subscription ID: $subscriptionId');

      final uri = Uri.parse(ApiEndpoints.updateSubscriptionId);
      final Map<String, String> headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
      };

      final Map<String, String> body = {
        'user_id': userId,
        'subscription_id': subscriptionId,
      };

      final response = await http.post(
        uri,
        headers: headers,
        body: body,
      );

      debugPrint('Response status: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          debugPrint('Subscription ID atualizado com sucesso');
        } else {
          debugPrint('Erro ao atualizar subscription_id: ${data['message']}');
        }
      } else {
        throw Exception('Erro na requisição: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Erro ao enviar dados para o backend: $e');
    }
  }

  // Função que exibe o Bottom Sheet com as opções
  void _showRegisterOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                  title: const Text('Cliente'),
                  trailing: const Icon(Icons.arrow_forward),
                  onTap: () {
                    Navigator.pop(context); // Fecha o Bottom Sheet
                    Navigator.pushNamed(context, '/client_register');
                  }),
              const Divider(),
              ListTile(
                title: const Text('Profissional'),
                trailing: const Icon(Icons.arrow_forward),
                onTap: () {
                  Navigator.pop(context); // Fecha o Bottom Sheet
                  Navigator.pushNamed(context, '/professional_register');
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _checkBiometricStatus() async {
    final enabled = await SecureStorageService.getBiometricEnabled();
    setState(() {
      _rememberMe = enabled;
    });
  }

  Future<void> _loadAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    final environment =
        const String.fromEnvironment('ENVIRONMENT', defaultValue: 'prod');

    setState(() {
      _appVersion =
          'Versão ${packageInfo.version} (Build ${packageInfo.buildNumber}) - $environment';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Container(
        color: Colors.white,
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                child: Column(
                  children: [
                    Container(
                      height: 300.h,
                      color: const Color.fromARGB(255, 246, 238, 221),
                      child: Center(
                        child: Image.asset(
                          'assets/logo.png',
                          height: 250.h,
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Entrar',
                              style: TextStyle(
                                fontSize: 24.sp, // Responsivo
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 20.h),

                            // Substituir a exibição do usuário único por uma lista de usuários
                            if (_savedUsers.isNotEmpty) ...[
                              Column(
                                children: _savedUsers.map((SavedUser user) {
                                  return Column(
                                    children: [
                                      ElevatedButton(
                                        onPressed: () =>
                                            _authenticateBiometrics(
                                                user.username),
                                        style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 16.h),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(16.r),
                                          ),
                                        ),
                                        child: Container(
                                          width: double.infinity,
                                          alignment: Alignment.center,
                                          child: Text(
                                            'Continuar como ${user.username} - ${user.typeDescription}',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium
                                                ?.copyWith(
                                                  fontSize: 16.sp,
                                                ),
                                          ),
                                        ),
                                      ),
                                      TextButton(
                                        onPressed: () =>
                                            _logoutBiometrics(user.username),
                                        child: Text(
                                          'Remover ${user.username}',
                                          style: TextStyle(
                                            color: Colors.red,
                                            fontSize: 14.sp,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: 8.h),
                                    ],
                                  );
                                }).toList(),
                              ),
                              SizedBox(height: 16.h),
                              TextButton(
                                onPressed: () {
                                  setState(() {
                                    _savedUsers = [];
                                  });
                                },
                                child: Text(
                                  'Usar outro usuário',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ),
                            ],
                            if (_savedUsers.isEmpty) ...[
                              TextFormField(
                                controller:
                                    _loginController, // Alterado para login
                                decoration: InputDecoration(
                                  labelText: 'Digite seu login',
                                  prefixIcon: Icon(Icons.person,
                                      size: 24.sp), // Responsivo
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Por favor, digite seu login';
                                  }
                                  // Validação do login
                                  if (!RegExp(r'^[a-z0-9]+(_[a-z0-9]+)?$')
                                      .hasMatch(value)) {
                                    return 'Login inválido. Use letras minúsculas, números e underscores.';
                                  }
                                  return null;
                                },
                              ),
                              SizedBox(height: 10.h),
                              TextFormField(
                                controller: _passwordController,
                                decoration: InputDecoration(
                                  labelText: 'Digite a senha',
                                  prefixIcon: Icon(Icons.lock, size: 24.sp),
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      _isPasswordVisible
                                          ? Icons.visibility
                                          : Icons.visibility_off,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _isPasswordVisible =
                                            !_isPasswordVisible;
                                      });
                                    },
                                  ),
                                ),
                                obscureText: !_isPasswordVisible,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Por favor, digite sua senha';
                                  }
                                  return null;
                                },
                              ),
                            ],
                            SizedBox(height: 10.h),
                            if (_savedUsers.isEmpty) ...[
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Checkbox(
                                        value: _rememberMe,
                                        onChanged: (value) {
                                          setState(() {
                                            _rememberMe = value ?? false;
                                          });
                                        },
                                      ),
                                      const Text('Ativar Biometria'),
                                    ],
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                ForgotPasswordScreen()),
                                      );
                                    },
                                    child: Text(
                                      'Esqueci a senha',
                                      style: TextStyle(
                                        color: const Color.fromRGBO(
                                            235, 30, 37, 1),
                                        fontSize: 14.sp, // Responsivo
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20.h),
                              ElevatedButton(
                                onPressed: _login,
                                style: ElevatedButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 16.h),
                                  minimumSize: Size(double.infinity, 48.h),
                                ),
                                child: Container(
                                  width: double.infinity,
                                  alignment: Alignment.center,
                                  child: Text(
                                    'Entrar',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 16.h),
                              OutlinedButton(
                                onPressed: () {
                                  Navigator.pushReplacementNamed(
                                      context, '/guest_home');
                                },
                                style: OutlinedButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 16.h),
                                  side: BorderSide(
                                      color: Theme.of(context).primaryColor),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                ),
                                child: Container(
                                  width: double.infinity,
                                  alignment: Alignment.center,
                                  child: Text(
                                    'Explorar como convidado',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 20.h),
                              Align(
                                alignment: Alignment.center,
                                child: RichText(
                                  text: TextSpan(
                                    text: 'Ainda não possui uma conta? ',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          fontSize: 14.sp,
                                        ),
                                    children: [
                                      TextSpan(
                                        text: 'Cadastre-se',
                                        style: TextStyle(
                                          color: const Color.fromRGBO(
                                              235, 30, 37, 1),
                                          fontSize: 14.sp, // Responsivo
                                        ),
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () {
                                            _showRegisterOptions(context);
                                          },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ]
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SafeArea(
              child: Padding(
                padding: EdgeInsets.only(bottom: 16.h),
                child: Text(
                  _appVersion,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
