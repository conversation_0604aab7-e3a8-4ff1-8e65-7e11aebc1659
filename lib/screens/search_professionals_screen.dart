import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../core/constants/api_endpoints.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';

class SearchProfessionalsScreen extends StatefulWidget {
  final int professionId;

  const SearchProfessionalsScreen({super.key, this.professionId = 0});

  @override
  _SearchProfessionalsScreenState createState() =>
      _SearchProfessionalsScreenState();
}

class _SearchProfessionalsScreenState extends State<SearchProfessionalsScreen> {
  late GoogleMapController mapController;
  Set<Marker> markers = {};
  Position? currentLocation;
  int professionId = 0;
  int requestId = 0;
  Timer? _timer;
  String username = '';
  String currentCity = '';
  String currentUF = '';
  String currentBairro = '';
  bool isLoading = true;
  double initialLat = -2.503675; // São Luís
  double initialLng = -44.200493; // São Luís
  List<Map<String, dynamic>> professionals = [];
  Map<String, bool> contactInitiated = {};
  Map<String, int> markerNumbers =
      {}; // Mapa para armazenar os números dos marcadores
  int nextMarkerNumber = 1; // Contador para o próximo número

  @override
  void initState() {
    super.initState();
    _initializeLocation();
    _excluirGPSProfissional(); // Chama ao entrar na tela
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final int newProfessionId =
        ModalRoute.of(context)?.settings.arguments as int? ?? 0;
    if (newProfessionId != professionId) {
      setState(() {
        professionId = newProfessionId;
      });
      _loadProfessionals(professionId);
    }
  }

  @override
  void dispose() {
    mapController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _loadUserId() async {
    final prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        username = prefs.getString('username')!;
      });
    }
  }

  Future<void> _updateLocationInfo(double latitude, double longitude) async {
    try {
      debugPrint(
          'Obtendo informações de localização para: $latitude, $longitude');
      List<Placemark> placemarks =
          await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        debugPrint('Informações obtidas:');
        debugPrint('Cidade: ${place.subAdministrativeArea}');
        debugPrint('Bairro: ${place.subLocality}');
        debugPrint('UF: ${place.administrativeArea}');

        if (mounted) {
          setState(() {
            currentCity = place.subAdministrativeArea ?? '';
            currentBairro = place.subLocality ?? '';
            currentUF = place.administrativeArea ?? '';
          });
        }
      }
    } catch (e) {
      debugPrint('Erro ao obter informações de localização: $e');
    }
  }

  Future<void> _initializeLocation() async {
    try {
      // Define as configurações de localização
      LocationSettings locationSettings = const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 100,
      );

      // Obtém a localização atual com as configurações definidas
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: locationSettings,
      );

      if (mounted) {
        setState(() {
          currentLocation = position;
          initialLat = position.latitude;
          initialLng = position.longitude;
        });

        // Atualiza as informações de localização
        await _updateLocationInfo(position.latitude, position.longitude);
      }

      _loadUserId();
      _startProfessionalsTimer();
    } catch (e) {
      debugPrint('Erro ao obter localização: $e');
      _showMessage('Erro ao obter localização: $e');
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  // Mapa para armazenar o tempo de adição de cada profissional
  Map<String, DateTime> professionalTimestamps = {};

  Future<void> _loadProfessionals(int professionId) async {
    final url = Uri.parse(ApiEndpoints.searchProfessionals);
    try {
      if (currentLocation == null) {
        debugPrint('Localização atual não disponível');
        return;
      }

/*
      debugPrint('Carregando profissionais:');
      debugPrint('URL: ${url.toString()}');
      debugPrint('''Parâmetros:
        profession_id: $professionId
        username: $username
        cidade: $currentCity
        bairro: $currentBairro
        uf: $currentUF
        latitude: ${currentLocation!.latitude}
        longitude: ${currentLocation!.longitude}
      ''');
*/

      final response = await http.post(
        url,
        body: {
          'profession_id': professionId.toString(),
          'username': username,
          'cidade': currentCity,
          'bairro': currentBairro,
          'uf': currentUF,
          'latitude': currentLocation!.latitude.toString(),
          'longitude': currentLocation!.longitude.toString(),
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true &&
            responseData.containsKey('professionals')) {
          final List<dynamic> professionalsList = responseData['professionals'];

          Set<Marker> newMarkers = {};
          Map<String, int> newMarkerNumbers = {};

          // Cria o marcador personalizado para localização atual
          final currentLocationIcon = await _createCurrentLocationMarker();

          // Adiciona o marcador da posição atual do usuário
          final currentLocationMarker = Marker(
            markerId: const MarkerId('current_location'),
            position:
                LatLng(currentLocation!.latitude, currentLocation!.longitude),
            icon: currentLocationIcon,
            infoWindow: const InfoWindow(
              title: 'Sua localização',
              snippet: 'Você está aqui',
            ),
            zIndex: 2, // Garante que fique acima dos outros marcadores
          );
          newMarkers.add(currentLocationMarker);

          // Adiciona os marcadores dos profissionais
          for (var professional in professionalsList) {
            try {
              double latitude =
                  double.parse(professional['latitude'].toString());
              double longitude =
                  double.parse(professional['longitude'].toString());
              String markerId = professional['id'].toString();

              int markerNumber = markerNumbers[markerId] ?? nextMarkerNumber++;
              newMarkerNumbers[markerId] = markerNumber;

              final markerIcon = await _createMarkerIcon(markerNumber);

              final marker = Marker(
                markerId: MarkerId(markerId),
                position: LatLng(latitude, longitude),
                icon: markerIcon,
                infoWindow: InfoWindow(
                  title: professional['nome'],
                  snippet: 'Toque para mais informações',
                ),
                zIndex: 1,
              );

              newMarkers.add(marker);
            } catch (e) {
              debugPrint('Erro ao criar marcador: $e');
            }
          }

          if (mounted) {
            setState(() {
              markers = newMarkers;
              markerNumbers = newMarkerNumbers;
              professionals =
                  List<Map<String, dynamic>>.from(professionalsList);
            });

            // Ajusta o zoom do mapa para mostrar todos os pontos
            _fitAllMarkersWithCurrentLocation();
          }
        }
      } else {
        _showMessage('Erro ao carregar profissionais: ${response.statusCode}');
      }
    } catch (e) {
      _showMessage('Erro ao carregar profissionais: $e');
    }
  }

  void _fitAllMarkersWithCurrentLocation() {
    if (markers.isEmpty) return;

    double minLat = double.infinity;
    double maxLat = -double.infinity;
    double minLng = double.infinity;
    double maxLng = -double.infinity;

    // Inclui todos os marcadores no cálculo dos limites
    for (Marker marker in markers) {
      minLat = min(minLat, marker.position.latitude);
      maxLat = max(maxLat, marker.position.latitude);
      minLng = min(minLng, marker.position.longitude);
      maxLng = max(maxLng, marker.position.longitude);
    }

    // Adiciona um padding para melhor visualização
    final double padding = 0.01; // Ajuste este valor conforme necessário
    minLat -= padding;
    maxLat += padding;
    minLng -= padding;
    maxLng += padding;

    mapController.animateCamera(
      CameraUpdate.newLatLngBounds(
        LatLngBounds(
          southwest: LatLng(minLat, minLng),
          northeast: LatLng(maxLat, maxLng),
        ),
        50.0, // padding em pixels
      ),
    );
  }

  String _buildRatingDetail(String rating) {
    return '$rating ★';
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _registerRequest(int professionalId) async {
    final url = Uri.parse(ApiEndpoints.registerRequest);
    try {
      // Carrega o userId das preferências
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('userId') ??
          ''; // Corrigido de 'user_id' para 'userId'

      final response = await http.post(
        url,
        body: {
          'client_id': userId,
          'professional_id': professionalId.toString(),
          'status': 'em_andamento',
        },
      );

      final data = json.decode(response.body);
      if (data['success']) {
        setState(() {
          requestId = data['service_request_id'];
        });
        _showMessage('Requisição registrada com sucesso');

        // Aguarda um pouco antes de redirecionar
        await Future.delayed(Duration(seconds: 1));
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/homeclient');
        }
      } else {
        debugPrint('Erro na resposta: ${data['message']}');
        _showMessage(
            'Erro ao registrar requisição: ${data['message'] ?? 'Erro desconhecido'}');
      }
    } catch (e) {
      _showMessage('Erro ao registrar requisição: $e');
    }
  }

  void _startProfessionalsTimer() {
    _timer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted) {
        _loadProfessionals(professionId);
      }
    });
  }

  Future<void> _openWhatsApp(String phoneNumber, String professionalId) async {
    final whatsappUrl = Uri.parse('https://wa.me/$phoneNumber');

    try {
      if (await canLaunchUrl(whatsappUrl)) {
        // Abre o WhatsApp
        await launchUrl(
          whatsappUrl,
          mode: LaunchMode.externalApplication,
        );

        // Marca que o contato foi iniciado
        setState(() {
          contactInitiated[professionalId] = true;
        });

        // Mostra um snackbar informativo, mas não persistente
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Caso contrate o profissional, volte aqui para confirmar'),
              duration: Duration(seconds: 4),
              action: SnackBarAction(
                label: 'OK',
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
            ),
          );
        }
      } else {
        _showMessage('Não foi possível abrir o WhatsApp');
      }
    } catch (e) {
      _showMessage('Erro ao abrir WhatsApp: $e');
    }
  }

  Future<void> _excluirGPSProfissional([String? professionalId]) async {
    final url = Uri.parse(ApiEndpoints.removeGPS);
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('userId') ?? '';

      final response = await http.post(
        url,
        body: {
          'professional_id': professionalId ?? userId,
        },
      );

      if (response.statusCode != 200) {
        _showMessage('Erro ao excluir GPS do profissional');
      }
    } catch (e) {
      _showMessage('Erro ao excluir GPS do profissional: $e');
    }
  }

  // Método para criar o ícone do marcador com número
  Future<BitmapDescriptor> _createMarkerIcon(int number) async {
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);
    final Paint paint = Paint()..color = Colors.red;
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: number.toString(),
        style: const TextStyle(
          fontSize: 30,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    canvas.drawCircle(const Offset(20, 20), 20, paint);

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        20 - textPainter.width / 2,
        20 - textPainter.height / 2,
      ),
    );

    final image = await pictureRecorder.endRecording().toImage(40, 40);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List uint8List = byteData!.buffer.asUint8List();

    // ignore: deprecated_member_use
    return BitmapDescriptor.fromBytes(uint8List);
  }

  Future<BitmapDescriptor> _createCurrentLocationMarker() async {
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);
    const double size = 40;

    final Paint borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(const Offset(size / 2, size / 2), size / 2, borderPaint);

    final Paint circlePaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;
    canvas.drawCircle(
        const Offset(size / 2, size / 2), (size / 2) - 2, circlePaint);

    final Paint centerPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(const Offset(size / 2, size / 2), 3, centerPaint);

    final ui.Image image = await pictureRecorder.endRecording().toImage(
          size.toInt(),
          size.toInt(),
        );
    final ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List uint8List = byteData!.buffer.asUint8List();

    // ignore: deprecated_member_use
    return BitmapDescriptor.fromBytes(uint8List);
  }

  // Opcional: Adicionar método para calcular e mostrar distâncias
  String _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    double distanceInMeters =
        Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.toStringAsFixed(0)}m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)}km';
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      // ignore: deprecated_member_use
      onPopInvoked: (bool didPop) async {
        if (didPop) return;

        bool shouldExit = await _showExitConfirmationDialog(context);
        if (shouldExit && context.mounted) {
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () async {
              bool shouldExit = await _showExitConfirmationDialog(context);
              if (shouldExit && mounted) {
                Navigator.pop(context);
              }
            },
          ),
          title: Text(
            'Buscar Profissionais',
            style: TextStyle(fontSize: 20.sp, color: Colors.black),
          ),
        ),
        body: Container(
          color: const Color.fromARGB(255, 246, 238, 221),
          child: SafeArea(
            child: Column(
              children: [
                Container(
                  height: MediaQuery.of(context).size.height * 0.35,
                  margin: EdgeInsets.all(8.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade300),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 5,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Stack(
                      children: [
                        GoogleMap(
                          onMapCreated: (GoogleMapController controller) {
                            mapController = controller;

                            // Atualiza a câmera quando o mapa é criado
                            Future.delayed(Duration(milliseconds: 500), () {
                              if (currentLocation != null) {
                                controller.animateCamera(
                                  CameraUpdate.newLatLngZoom(
                                    LatLng(currentLocation!.latitude,
                                        currentLocation!.longitude),
                                    15.0,
                                  ),
                                );
                              }
                            });
                          },
                          initialCameraPosition: CameraPosition(
                            target: LatLng(initialLat, initialLng),
                            zoom: 15.0,
                          ),
                          markers: markers,
                          myLocationEnabled: true,
                          myLocationButtonEnabled: true,
                          zoomControlsEnabled: true,
                          mapType: MapType.normal,
                          compassEnabled: true,
                        ),
                        if (isLoading)
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.8),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CircularProgressIndicator(),
                                  SizedBox(height: 16),
                                  Text(
                                    'Carregando mapa...',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),

                // Lista de profissionais
                Expanded(
                  child: markers.isEmpty || markers.length == 1
                      ? SingleChildScrollView(
                          child: Center(
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 24.w, vertical: 16.h),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Ícone da pasta com interrogação
                                  Icon(
                                    Icons.folder_outlined,
                                    size: 60.sp,
                                    color: Colors.grey[400],
                                  ),
                                  SizedBox(height: 8.h),
                                  // Ícone de interrogação
                                  Container(
                                    padding: EdgeInsets.all(6.w),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[300],
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.question_mark,
                                      size: 20.sp,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(height: 16.h),
                                  // Texto principal
                                  Text(
                                    'Nenhum profissional atendeu ainda!',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey[800],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: 8.h),
                                  // Texto secundário
                                  Text(
                                    'Aguarde mais um pouco...',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: Colors.grey[600],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: EdgeInsets.all(16.w),
                          itemCount: markers.length,
                          itemBuilder: (context, index) {
                            final marker = markers.elementAt(index);
                            final professional = professionals.firstWhere(
                              (p) =>
                                  p['id'].toString() == marker.markerId.value,
                              orElse: () => {},
                            );

                            if (professional.isEmpty) {
                              return const SizedBox.shrink();
                            }

                            final markerNumber =
                                markerNumbers[professional['id'].toString()] ??
                                    0;

                            // Calcula a distância
                            final distance = _calculateDistance(
                              currentLocation!.latitude,
                              currentLocation!.longitude,
                              double.parse(professional['latitude'].toString()),
                              double.parse(
                                  professional['longitude'].toString()),
                            );

                            return Card(
                              margin: EdgeInsets.only(bottom: 16.h),
                              color: Theme.of(context).primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                              child: Padding(
                                padding: EdgeInsets.all(12.w),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        // Número do marcador
                                        Container(
                                          width: 30,
                                          height: 30,
                                          decoration: BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Center(
                                            child: Text(
                                              markerNumber.toString(),
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16.sp,
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 12.w),

                                        // Nome do profissional
                                        Expanded(
                                          child: Text(
                                            professional['nome'],
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ),

                                        // Distância
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 8.w, vertical: 4.h),
                                          decoration: BoxDecoration(
                                            color: Colors.grey[200],
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons.location_on,
                                                size: 16.sp,
                                                color: Colors.grey[600],
                                              ),
                                              SizedBox(width: 4.w),
                                              Text(
                                                distance,
                                                style: TextStyle(
                                                  fontSize: 14.sp,
                                                  color: Colors.grey[600],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),

                                        SizedBox(width: 12.w),

                                        // Avaliação
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 8.w, vertical: 4.h),
                                          decoration: BoxDecoration(
                                            color: Colors.amber[100],
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Icon(
                                                Icons.star,
                                                size: 16.sp,
                                                color: Colors.amber,
                                              ),
                                              SizedBox(width: 4.w),
                                              Text(
                                                _buildRatingDetail(
                                                    professional['rating']),
                                                style: TextStyle(
                                                  fontSize: 14.sp,
                                                  color: Colors.grey[800],
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 12.h),
                                    // Linha de botões
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        // Mostra Descartar e Contratar apenas se o contato foi iniciado
                                        if (contactInitiated[professional['id']
                                                .toString()] ==
                                            true) ...[
                                          // Botão Descartar
                                          ElevatedButton.icon(
                                            icon: const FaIcon(
                                              FontAwesomeIcons.trash,
                                              color: Colors.white,
                                              size: 16,
                                            ),
                                            label: Text(
                                              'Descartar',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14.sp,
                                              ),
                                            ),
                                            onPressed: () async {
                                              try {
                                                await _excluirGPSProfissional(
                                                    professional['id']
                                                        .toString());
                                                setState(() {
                                                  markers.removeWhere((m) =>
                                                      m.markerId.value ==
                                                      marker.markerId.value);
                                                  professionals.removeWhere(
                                                      (p) =>
                                                          p['id'].toString() ==
                                                          professional['id']
                                                              .toString());
                                                });
                                              } catch (e) {
                                                _showMessage(
                                                    'Erro ao remover profissional: $e');
                                              }
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.red,
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 12.w,
                                                vertical: 8.h,
                                              ),
                                            ),
                                          ),

                                          // Botão Contratar
                                          ElevatedButton.icon(
                                            icon: const FaIcon(
                                              FontAwesomeIcons.handshake,
                                              color: Colors.white,
                                              size: 16,
                                            ),
                                            label: Text(
                                              'Contratei',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14.sp,
                                              ),
                                            ),
                                            onPressed: () {
                                              final professionalId = int.parse(
                                                  professional['id']
                                                      .toString());
                                              _registerRequest(professionalId);
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.green,
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 12.w,
                                                vertical: 8.h,
                                              ),
                                            ),
                                          ),
                                        ],

                                        // Mostra Entrar em contato apenas se o contato não foi iniciado
                                        if (contactInitiated[professional['id']
                                                .toString()] !=
                                            true)
                                          ElevatedButton.icon(
                                            icon: const FaIcon(
                                              FontAwesomeIcons.whatsapp,
                                              color: Colors.white,
                                            ),
                                            label: Text(
                                              'Entre em contato',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 14.sp,
                                              ),
                                            ),
                                            onPressed: () async {
                                              final phoneNumber =
                                                  professional['celular'];
                                              await _openWhatsApp(
                                                  phoneNumber,
                                                  professional['id']
                                                      .toString());
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.green,
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 12.w,
                                                vertical: 8.h,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<bool> _showExitConfirmationDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Sair da tela',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Se você sair agora, perderá todas as informações dos profissionais disponíveis. Deseja realmente sair?',
            style: TextStyle(fontSize: 16.sp),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'Cancelar',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey,
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: Text(
                'Sair',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.red,
                ),
              ),
              onPressed: () async {
                // Primeiro exclui os dados do GPS
                for (var professional in professionals) {
                  await _excluirGPSProfissional(professional['id'].toString());
                }
                if (mounted) {
                  Navigator.of(context).pop(true);
                }
              },
            ),
          ],
        );
      },
    );

    return result ?? false;
  }
}
