import 'dart:convert';
import 'package:epraja/screens/privacy_policy.dart';
import 'package:epraja/screens/terms_conditions.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:epraja/core/constants/api_endpoints.dart';
import 'package:epraja/core/config/environment.dart';

class HomeScreenClient extends StatefulWidget {
  const HomeScreenClient({super.key});

  @override
  _HomeScreenClientState createState() => _HomeScreenClientState();
}

class _HomeScreenClientState extends State<HomeScreenClient> {
  List<Map<String, dynamic>> professions = [];
  List<Map<String, dynamic>> filteredProfessions = [];
  TextEditingController searchController = TextEditingController();
  TextEditingController demandController = TextEditingController();
  String? selectedProfession;
  String username = '';
  String user = '';
  String _appVersion = 'Carregando...';

  @override
  void initState() {
    super.initState();
    _loadSavedData();
    _loadProfessions();
    searchController.addListener(_filterProfessions);
    _loadAppVersion();
  }

  Future<void> _loadAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String environment = EnvironmentConfig.isDevelopment ? 'Dev' : 'Prod';

    setState(() {
      _appVersion =
          'Versão ${packageInfo.version} (Build ${packageInfo.buildNumber}) - $environment';
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    demandController.dispose();
    super.dispose();
  }

  void _onProfessionSelected(int professionId) {
    setState(() {
      selectedProfession = filteredProfessions
          .firstWhere((prof) => prof['id'] == professionId)['profession'];
      demandController.clear();
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, StateSetter setDialogState) {
            return AlertDialog(
              title: const Text('O que você precisa?'),
              content: TextField(
                controller: demandController,
                decoration: const InputDecoration(
                  labelText: 'Digite o que estás precisando',
                ),
              ),
              actions: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: const Text('Cancelar'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            if (demandController.text.isNotEmpty) {
                              _sendNotification(
                                  professionId, demandController.text);
                              Navigator.pushNamed(
                                context,
                                '/search_professionals',
                                arguments: professionId,
                              );
                            } else {
                              _showMessage('Por favor, digite sua demanda.');
                            }
                          },
                          child: const Text('Enviar'),
                        ),
                      ],
                    ),
                    SizedBox(height: 16.h),
                    Container(
                      alignment: Alignment.center,
                      width: 320,
                      height: 50,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white),
                        color: Colors.white,
                      ),
                      child: const Text('Espaço para Anúncio'),
                    ),
                  ],
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _sendNotification(int professionId, String demand) async {
    Position position = await _getCurrentLocation();
    String neighborhood = await _getNeighborhoodFromPosition(position);

    debugPrint('Profissão selecionada: $professionId');
    debugPrint('Demanda: $demand');
    debugPrint('Bairro: $neighborhood');
    debugPrint('Latitude: ${position.latitude}');
    debugPrint('Longitude: ${position.longitude}');

    final url = Uri.parse(ApiEndpoints.sendNotification);
    try {
      final response = await http.post(
        url,
        body: {
          'username': user,
          'profession_id': professionId.toString(),
          'mensagem': demand,
          'bairro': neighborhood,
          'latitude': position.latitude.toString(),
          'longitude': position.longitude.toString(),
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        debugPrint(data.toString());

        if (data['status'] == 'success') {
          _showMessage('Notificação enviada com sucesso');
          debugPrint('Notificação enviada com sucesso');
        } else {
          _showMessage('Erro no envio da notificação: ${data['message']}');
          debugPrint('Erro no envio da notificação: ${data['message']}');
        }
      } else {
        _showMessage(
            'Erro durante o envio da notificação: ${response.statusCode}');
      }
    } catch (e) {
      _showMessage('Erro ao enviar notificação: $e');
      debugPrint('Erro ao enviar notificação: $e');
    }
  }

  Future<Position> _getCurrentLocation() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }
    if (permission == LocationPermission.denied) {
      throw Exception('Permissão de localização negada');
    }

    return await Geolocator.getCurrentPosition(
      locationSettings: LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    );
  }

  Future<String> _getNeighborhoodFromPosition(Position position) async {
    try {
      List<Placemark> placemarks =
          await placemarkFromCoordinates(position.latitude, position.longitude);
      if (placemarks.isNotEmpty) {
        return placemarks.first.subLocality ?? 'Bairro não encontrado';
      } else {
        return 'Bairro não encontrado';
      }
    } catch (e) {
      _showMessage('Erro ao obter o bairro: $e');
      return 'Bairro não encontrado';
    }
  }

  Future<void> _loadProfessions() async {
    final response = await http.get(Uri.parse(ApiEndpoints.getProfessions));
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success']) {
        setState(() {
          professions = (data['professions'] as List)
              .map((prof) => {
                    'id': prof['id'],
                    'profession': prof['profession'],
                    'logo': prof['logo'],
                  })
              .toList();
          filteredProfessions = professions;
        });
      }
    }
  }

  void _filterProfessions() {
    final query = searchController.text.toLowerCase();
    setState(() {
      filteredProfessions = professions.where((profession) {
        final professionName = profession['profession'].toLowerCase();
        return professionName.startsWith(query);
      }).toList();
    });
  }

  Future<void> _loadSavedData() async {
    final prefs = await SharedPreferences.getInstance();
    user = prefs.getString('username')!;
    username = prefs.getString('name')!;
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('username');
    await prefs.remove('password');
    await prefs.remove('userId');
    await prefs.remove('name');
    Navigator.pushReplacementNamed(context, '/login');
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Excluir Conta'),
          content:
              Text('Tem certeza de que quer que os seus dados sejam apagados?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Fecha o diálogo
              },
              child: Text('CANCELAR'),
            ),
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Fecha o diálogo
                _deleteAccount(); // Chama a função para excluir a conta
              },
              child: Text('SIM'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteAccount() async {
    try {
      final url = Uri.parse(ApiEndpoints.deleteUserId);
      final response = await http.post(
        url,
        body: {
          'username': user,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          _showMessage(data['message']);
          // Aguarda um pouco para mostrar a mensagem antes de fazer logout
          await Future.delayed(const Duration(seconds: 2));
          _logout(); // Faz logout após excluir a conta
        } else {
          _showMessage(data['message']);
        }
      } else {
        _showMessage('Erro ao excluir conta: ${response.statusCode}');
      }
    } catch (e) {
      _showMessage('Erro ao excluir conta: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(414, 896),
      builder: (context, child) {
        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            elevation: 0,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Buscar Profissionais', style: TextStyle(fontSize: 20.sp)),
                Image.asset(
                  'assets/logo.png',
                  height: 70.h,
                ),
              ],
            ),
          ),
          drawer: Drawer(
            child: Column(
              children: <Widget>[
                UserAccountsDrawerHeader(
                  accountName: Text(username,
                      style: TextStyle(fontSize: 17.sp, color: Colors.black)),
                  accountEmail: Text('Cliente',
                      style: TextStyle(fontSize: 15.sp, color: Colors.black)),
                  currentAccountPicture: CircleAvatar(
                    backgroundColor: Colors.white,
                    child: ClipOval(
                      child: Image.asset('assets/user.png', fit: BoxFit.cover),
                    ),
                  ),
                ),
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    children: <Widget>[
                      ListTile(
                        leading: Icon(Icons.person_add, size: 24.sp),
                        title: Text('Cadastre-se como Profissional',
                            style: TextStyle(fontSize: 14.sp)),
                        onTap: () {
                          Navigator.pushNamed(
                              context, '/professional_register');
                        },
                      ),
                      ListTile(
                        leading:
                            Icon(Icons.miscellaneous_services, size: 24.sp),
                        title: Text('Serviços Contratados',
                            style: TextStyle(fontSize: 14.sp)),
                        onTap: () {
                          Navigator.pushNamed(context, '/search_services');
                        },
                      ),
                      ListTile(
                        leading: Icon(Icons.assignment),
                        title: Text('Termos e Condições de Uso',
                            style: TextStyle(fontSize: 14.sp)),
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      TermsAndConditionsScreen()));
                        },
                      ),
                      ListTile(
                        leading: Icon(Icons.description),
                        title: Text('Política de Privacidade',
                            style: TextStyle(fontSize: 14.sp)),
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => PrivacyPolicyScreen()));
                        },
                      ),
                      ListTile(
                        leading: Icon(Icons.delete_forever, color: Colors.red),
                        title: Text('Excluir Conta',
                            style:
                                TextStyle(fontSize: 14.sp, color: Colors.red)),
                        onTap: () {
                          _showDeleteAccountDialog();
                        },
                      ),
                      ListTile(
                        leading: Icon(Icons.logout),
                        title: Text('Sair', style: TextStyle(fontSize: 14.sp)),
                        onTap: _logout,
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _appVersion,
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                ),
              ],
            ),
          ),
          body: SafeArea(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.all(16.w),
                  child: TextField(
                    controller: searchController,
                    decoration: const InputDecoration(
                      labelText: 'Digite uma profissão',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  width: double.infinity,
                  height: 80,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white),
                    color: Colors.white,
                  ),
                  child: const Text('Espaço para Anúncio'),
                ),
                Expanded(
                  child: GridView.builder(
                    padding: EdgeInsets.all(16.w),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      childAspectRatio: 1,
                      mainAxisSpacing: 16.w,
                      crossAxisSpacing: 16.w,
                    ),
                    itemCount: filteredProfessions.length,
                    itemBuilder: (context, index) {
                      return ElevatedButton(
                        onPressed: () {
                          _onProfessionSelected(
                              filteredProfessions[index]['id']);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: EdgeInsets.zero,
                        ),
                        child: Center(
                          child: Image.memory(
                            base64Decode(filteredProfessions[index]['logo']),
                            height: 150.h,
                            width: 150.w,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
