import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage();

  // Chaves para os dados armazenados
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _userTypeKey = 'user_type';
  static const String _biometricEnabledKey = 'biometric_enabled';

  // Salvar token
  static Future<void> saveToken(String token) async {
    await _storage.write(key: _tokenKey, value: token);
  }

  // Obter token
  static Future<String?> getToken() async {
    return await _storage.read(key: _tokenKey);
  }

  // Salvar ID do usuário
  static Future<void> saveUserId(String userId) async {
    await _storage.write(key: _userIdKey, value: userId);
  }

  // Obter ID do usuário
  static Future<String?> getUserId() async {
    return await _storage.read(key: _userIdKey);
  }

  // Salvar tipo de usuário (cliente ou profissional)
  static Future<void> saveUserType(String userType) async {
    await _storage.write(key: _userTypeKey, value: userType);
  }

  // Obter tipo de usuário
  static Future<String?> getUserType() async {
    return await _storage.read(key: _userTypeKey);
  }

  // Salvar status da biometria
  static Future<void> saveBiometricEnabled(bool enabled) async {
    await _storage.write(key: _biometricEnabledKey, value: enabled.toString());
  }

  // Obter status da biometria
  static Future<bool> getBiometricEnabled() async {
    final value = await _storage.read(key: _biometricEnabledKey);
    return value == 'true';
  }

  // Limpar todos os dados armazenados (útil para logout)
  static Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  // Remover um item específico
  static Future<void> removeItem(String key) async {
    await _storage.delete(key: key);
  }
}
