import "package:flutter/material.dart";

class MaterialTheme {
  final TextTheme textTheme;

  const MaterialTheme(this.textTheme);

  static ColorScheme lightScheme() {
    return const ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xFFF9C242), // Cor principal laranja
      onPrimary: Color(0xFF201A17), // Texto branco sobre a cor principal
      primaryContainer: Color(0xFFFFDBBA), // Um tom mais claro para contrastar
      onPrimaryContainer: Color(0xFF3A1D00),
      secondary:
          Color(0xFFCC4A2C), // Um vermelho profundo, complementar ao laranja
      onSecondary: Color(0xFFFFFFFF),
      secondaryContainer: Color(0xFFFFDBBA),
      onSecondaryContainer: Color(0xFF3A1D00),
      tertiary: Color(0xFFAA7326), // Marrom amarelado, harmonioso com o laranja
      onTertiary: Color(0xFFFFFFFF),
      tertiaryContainer: Color(0xFFFFDDBB),
      onTertiaryContainer: Color(0xFF3A1D00),
      error: Color(0xFFD32F2F), // Vermelho mais neutro para erro
      errorContainer: Color(0xFFFFDAD4),
      onError: Color(0xFFFFFFFF),
      onErrorContainer: Color(0xFF410000),
      surface: Color.fromARGB(255, 255, 255, 255),
      onSurface: Color(0xFF201A17),
      surfaceContainerHighest: Color(0xFFF4DED3),
      onSurfaceVariant: Color(0xFF52443C),
      outline: Color(0xFF85746A),
      onInverseSurface: Color(0xFFFBEEE9),
      inverseSurface: Color(0xFF362F2B),
      inversePrimary: Color(0xFFFFB87D),
      shadow: Color(0xFF000000),
      surfaceTint: Color(0xFFE08536), // Mantém o laranja
      outlineVariant: Color(0xFFD8C2B7),
      scrim: Color(0xFF000000),
      primaryFixed: Color(0xFFFFDBBA),
      primaryFixedDim:
          Color(0xFFCC7630), // Tom mais escuro do laranja para superfícies
      onPrimaryFixed: Color(0xFF3A1D00),
      onPrimaryFixedVariant: Color(0xFFFFDBBA),
    );
  }

  ThemeData light() {
    return theme(lightScheme());
  }

  static ColorScheme lightMediumContrastScheme() {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary:
          Color(0xFFFFB87D), // Versão mais clara do laranja para o tema escuro
      onPrimary: Color(0xFF5D3200),
      primaryContainer: Color(0xFF834714),
      onPrimaryContainer: Color(0xFFFFDBBA),
      secondary: Color(0xFFE7BFAF),
      onSecondary: Color(0xFF4A2714),
      secondaryContainer: Color(0xFF653D29),
      onSecondaryContainer: Color(0xFFFFDBBA),
      tertiary: Color(0xFFDDC2A1), // Marrom amarelado suavizado
      onTertiary: Color(0xFF3F2D14),
      tertiaryContainer: Color(0xFF573F22),
      onTertiaryContainer: Color(0xFFFFDDBB),
      error: Color(0xFFFFB4AB),
      errorContainer: Color(0xFF93000A),
      onError: Color(0xFF690005),
      onErrorContainer: Color(0xFFFFDAD4),
      surface: Color(0xFF201A17),
      onSurface: Color(0xFFEDE0DB),
      surfaceContainerHighest: Color(0xFF52443C),
      onSurfaceVariant: Color(0xFFD8C2B7),
      outline: Color(0xFF9F8D83),
      onInverseSurface: Color(0xFF201A17),
      inverseSurface: Color(0xFFEDE0DB),
      inversePrimary: Color(0xFF835414),
      shadow: Color(0xFF000000),
      surfaceTint: Color(0xFFFFB87D),
      outlineVariant: Color(0xFF52443C),
      scrim: Color(0xFF000000),
      primaryFixed: Color(0xFF9C662A),
      primaryFixedDim:
          Color(0xFF6B4A18), // Escurecido para melhor contraste no tema escuro
      onPrimaryFixed: Color(0xFFFFDBBA),
      onPrimaryFixedVariant: Color(0xFFFFDDBB),
    );
  }

  ThemeData lightMediumContrast() {
    return theme(lightMediumContrastScheme());
  }

  static ColorScheme lightHighContrastScheme() {
    return const ColorScheme(
      brightness: Brightness.light,
      primary: Color(0xff341d00),
      surfaceTint: Color(0xff835414),
      onPrimary: Color(0xffffffff),
      primaryContainer: Color(0xff613a00),
      onPrimaryContainer: Color(0xffffffff),
      secondary: Color(0xff3d1700),
      onSecondary: Color(0xffffffff),
      secondaryContainer: Color(0xff6a340f),
      onSecondaryContainer: Color(0xffffffff),
      tertiary: Color(0xff2e2000),
      onTertiary: Color(0xffffffff),
      tertiaryContainer: Color(0xff573f00),
      onTertiaryContainer: Color(0xffffffff),
      error: Color(0xff4e0002),
      onError: Color(0xffffffff),
      errorContainer: Color(0xff8c0009),
      onErrorContainer: Color(0xffffffff),
      surface: Color(0xfffff8f4),
      onSurface: Color(0xff000000),
      onSurfaceVariant: Color(0xff2b2218),
      outline: Color(0xff4c4136),
      outlineVariant: Color(0xff4c4136),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xff372f27),
      inversePrimary: Color(0xffffe8d3),
      primaryFixed: Color(0xff613a00),
      onPrimaryFixed: Color(0xffffffff),
      primaryFixedDim: Color(0xff422600),
      onPrimaryFixedVariant: Color(0xffffffff),
      secondaryFixed: Color(0xff6a340f),
      onSecondaryFixed: Color(0xffffffff),
      secondaryFixedDim: Color(0xff4d1f00),
      onSecondaryFixedVariant: Color(0xffffffff),
      tertiaryFixed: Color(0xff573f00),
      onTertiaryFixed: Color(0xffffffff),
      tertiaryFixedDim: Color(0xff3c2a00),
      onTertiaryFixedVariant: Color(0xffffffff),
      surfaceDim: Color(0xffe5d8cc),
      surfaceBright: Color(0xfffff8f4),
      surfaceContainerLowest: Color(0xffffffff),
      surfaceContainerLow: Color(0xfffff1e6),
      surfaceContainer: Color(0xfffaebe0),
      surfaceContainerHigh: Color(0xfff4e6da),
      surfaceContainerHighest: Color(0xffeee0d5),
    );
  }

  ThemeData lightHighContrast() {
    return theme(lightHighContrastScheme());
  }

  static ColorScheme darkScheme() {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xfff9bb72),
      surfaceTint: Color(0xfff9bb72),
      onPrimary: Color(0xff472a00),
      primaryContainer: Color(0xff663e00),
      onPrimaryContainer: Color(0xffffddb9),
      secondary: Color(0xffffb68d),
      onSecondary: Color(0xff532200),
      secondaryContainer: Color(0xff6f3813),
      onSecondaryContainer: Color(0xffffdbc9),
      tertiary: Color(0xffebc16c),
      onTertiary: Color(0xff402d00),
      tertiaryContainer: Color(0xff5c4200),
      onTertiaryContainer: Color(0xffffdea2),
      error: Color(0xffffb4ab),
      onError: Color(0xff690005),
      errorContainer: Color(0xff93000a),
      onErrorContainer: Color(0xffffdad6),
      surface: Color(0xff18120c),
      onSurface: Color(0xffffffff),
      onSurfaceVariant: Color(0xffd4c4b5),
      outline: Color(0xff9d8e81),
      outlineVariant: Color(0xff504539),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xffeee0d5),
      inversePrimary: Color(0xff835414),
      primaryFixed: Color(0xffffddb9),
      onPrimaryFixed: Color(0xff2b1700),
      primaryFixedDim: Color(0xfff9bb72),
      onPrimaryFixedVariant: Color(0xff663e00),
      secondaryFixed: Color(0xffffdbc9),
      onSecondaryFixed: Color(0xff331200),
      secondaryFixedDim: Color(0xffffb68d),
      onSecondaryFixedVariant: Color(0xff6f3813),
      tertiaryFixed: Color(0xffffdea2),
      onTertiaryFixed: Color(0xff261900),
      tertiaryFixedDim: Color(0xffebc16c),
      onTertiaryFixedVariant: Color(0xff5c4200),
      surfaceDim: Color(0xff18120c),
      surfaceBright: Color(0xff403830),
      surfaceContainerLowest: Color(0xff130d07),
      surfaceContainerLow: Color(0xff211a13),
      surfaceContainer: Color(0xff251e17),
      surfaceContainerHigh: Color(0xff302921),
      surfaceContainerHighest: Color(0xff3b332c),
    );
  }

  ThemeData dark() {
    return theme(darkScheme());
  }

  static ColorScheme darkMediumContrastScheme() {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xfffebf75),
      surfaceTint: Color(0xfff9bb72),
      onPrimary: Color(0xff241200),
      primaryContainer: Color(0xffbd8642),
      onPrimaryContainer: Color(0xff000000),
      secondary: Color(0xffffbc97),
      onSecondary: Color(0xff2a0e00),
      secondaryContainer: Color(0xffc87f54),
      onSecondaryContainer: Color(0xff000000),
      tertiary: Color(0xffefc570),
      onTertiary: Color(0xff1f1400),
      tertiaryContainer: Color(0xffb08b3d),
      onTertiaryContainer: Color(0xff000000),
      error: Color(0xffffbab1),
      onError: Color(0xff370001),
      errorContainer: Color(0xffff5449),
      onErrorContainer: Color(0xff000000),
      surface: Color(0xff18120c),
      onSurface: Color(0xFF201A17),
      onSurfaceVariant: Color(0xffd8c8b9),
      outline: Color(0xffafa092),
      outlineVariant: Color(0xff8f8173),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xffeee0d5),
      inversePrimary: Color(0xff683f00),
      primaryFixed: Color(0xffffddb9),
      onPrimaryFixed: Color(0xff1d0e00),
      primaryFixedDim: Color(0xfff9bb72),
      onPrimaryFixedVariant: Color(0xff4f2f00),
      secondaryFixed: Color(0xffffdbc9),
      onSecondaryFixed: Color(0xff220a00),
      secondaryFixedDim: Color(0xffffb68d),
      onSecondaryFixedVariant: Color(0xff5a2804),
      tertiaryFixed: Color(0xffffdea2),
      onTertiaryFixed: Color(0xff191000),
      tertiaryFixedDim: Color(0xffebc16c),
      onTertiaryFixedVariant: Color(0xff473300),
      surfaceDim: Color(0xff18120c),
      surfaceBright: Color(0xff403830),
      surfaceContainerLowest: Color(0xff130d07),
      surfaceContainerLow: Color(0xff211a13),
      surfaceContainer: Color(0xff251e17),
      surfaceContainerHigh: Color(0xff302921),
      surfaceContainerHighest: Color(0xff3b332c),
    );
  }

  ThemeData darkMediumContrast() {
    return theme(darkMediumContrastScheme());
  }

  static ColorScheme darkHighContrastScheme() {
    return const ColorScheme(
      brightness: Brightness.dark,
      primary: Color(0xfffffaf8),
      surfaceTint: Color(0xfff9bb72),
      onPrimary: Color(0xff000000),
      primaryContainer: Color(0xfffebf75),
      onPrimaryContainer: Color(0xff000000),
      secondary: Color(0xfffffaf8),
      onSecondary: Color(0xff000000),
      secondaryContainer: Color(0xffffbc97),
      onSecondaryContainer: Color(0xff000000),
      tertiary: Color(0xfffffaf7),
      onTertiary: Color(0xff000000),
      tertiaryContainer: Color(0xffefc570),
      onTertiaryContainer: Color(0xff000000),
      error: Color(0xfffff9f9),
      onError: Color(0xff000000),
      errorContainer: Color(0xffffbab1),
      onErrorContainer: Color(0xff000000),
      surface: Color(0xff18120c),
      onSurface: Color(0xffffffff),
      onSurfaceVariant: Color(0xfffffaf8),
      outline: Color(0xffd8c8b9),
      outlineVariant: Color(0xffd8c8b9),
      shadow: Color(0xff000000),
      scrim: Color(0xff000000),
      inverseSurface: Color(0xffeee0d5),
      inversePrimary: Color(0xff3f2400),
      primaryFixed: Color(0xffffe2c5),
      onPrimaryFixed: Color(0xff000000),
      primaryFixedDim: Color(0xfffebf75),
      onPrimaryFixedVariant: Color(0xff241200),
      secondaryFixed: Color(0xffffe1d2),
      onSecondaryFixed: Color(0xff000000),
      secondaryFixedDim: Color(0xffffbc97),
      onSecondaryFixedVariant: Color(0xff2a0e00),
      tertiaryFixed: Color(0xffffe3b2),
      onTertiaryFixed: Color(0xff000000),
      tertiaryFixedDim: Color(0xffefc570),
      onTertiaryFixedVariant: Color(0xff1f1400),
      surfaceDim: Color(0xff18120c),
      surfaceBright: Color(0xff403830),
      surfaceContainerLowest: Color(0xff130d07),
      surfaceContainerLow: Color(0xff211a13),
      surfaceContainer: Color(0xff251e17),
      surfaceContainerHigh: Color(0xff302921),
      surfaceContainerHighest: Color(0xff3b332c),
    );
  }

  ThemeData darkHighContrast() {
    return theme(darkHighContrastScheme());
  }

  ThemeData theme(ColorScheme colorScheme) => ThemeData(
        useMaterial3: true,
        brightness: colorScheme.brightness,
        colorScheme: colorScheme,
        textTheme: TextTheme(
          bodyMedium: TextStyle(
            color: colorScheme.onSurface, // Herda a cor do tema
            fontSize: 14.0, // Tamanho da fonte
          ),
          // Outros estilos podem ser definidos aqui, como bodyLarge, headline1, etc.
        ).apply(
          bodyColor: colorScheme.onSurface,
          displayColor: colorScheme.onSurface,
        ),
        inputDecorationTheme: InputDecorationTheme(
          floatingLabelStyle: TextStyle(
            color: colorScheme.onSurface, // Cor do label quando flutuando
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: colorScheme.onSurface, // Cor da borda ao focar
            ),
          ),
        ),
        scaffoldBackgroundColor: colorScheme.surface,
        canvasColor: colorScheme.surface,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.primary, // Cor de fundo do botão
            foregroundColor: Colors.black, // Texto sempre preto
            shape: RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(16), // Arredondamento dos botões
            ),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor:
                colorScheme.onSurfaceVariant, // Cor do texto no TextButton
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor:
                colorScheme.primary, // Cor do texto no OutlinedButton
            side: BorderSide(color: colorScheme.primary), // Cor da borda
          ),
        ),
      );

  List<ExtendedColor> get extendedColors => [];
}

class ExtendedColor {
  final Color seed, value;
  final ColorFamily light;
  final ColorFamily lightHighContrast;
  final ColorFamily lightMediumContrast;
  final ColorFamily dark;
  final ColorFamily darkHighContrast;
  final ColorFamily darkMediumContrast;

  const ExtendedColor({
    required this.seed,
    required this.value,
    required this.light,
    required this.lightHighContrast,
    required this.lightMediumContrast,
    required this.dark,
    required this.darkHighContrast,
    required this.darkMediumContrast,
  });
}

class ColorFamily {
  const ColorFamily({
    required this.color,
    required this.onColor,
    required this.colorContainer,
    required this.onColorContainer,
  });

  final Color color;
  final Color onColor;
  final Color colorContainer;
  final Color onColorContainer;
}
