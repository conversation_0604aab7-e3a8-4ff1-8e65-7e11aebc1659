#!/bin/bash

# Limpar o cache do Xcode (DerivedData e caches)
echo "Limpando caches do Xcode..."
rm -rf ~/Library/Developer/Xcode/DerivedData
rm -rf ~/Library/Caches/com.apple.dt.Xcode

# Limpar o cache de módulos e estatísticas do SDK
echo "Limpando caches de módulos e estatísticas do SDK..."
rm -rf ~/Library/Developer/Xcode/DerivedData/ModuleCache.noindex
rm -rf ~/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex

# Limpar o cache de pods
echo "Limpando cache do CocoaPods..."
pod cache clean --all

# Deintegrar os pods do projeto
echo "Deintegrando os pods do projeto..."
cd ios
pod deintegrate
rm -rf Pods
rm -rf Podfile.lock
cd ..

# Limpar o projeto Flutter
echo "Limpando o projeto Flutter..."
flutter clean
flutter pub get

# Reinstalar os pods
echo "Reinstalando os pods..."
cd ios
pod install --repo-update

echo "Limpeza concluída!" 