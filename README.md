# epraja

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.dev/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.dev/docs/cookbook)

For help getting started with Flutter, view our
[online documentation](https://flutter.dev/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

 Como versionar e atualizar o projeto no GitHub

### 1. Inicializar o repositório Git (apenas na primeira vez)
```sh
cd "/Users/<USER>/Documents/Projetos Flutter/epraja"
git init
```

### 2. Adicionar todos os arquivos e fazer o primeiro commit
```sh
git add .
git commit -m "Primeiro commit: projeto inicial"
```

### 3. Adicionar o repositório remoto do GitHub
No GitHub, crie um repositório (ex: epsique) e copie a URL. Depois execute:
```sh
git remote add origin https://github.com/seu-usuario/epraja.git
```

### 4. Enviar o projeto para o GitHub
```sh
git branch -M main
git push -u origin main
```

### 5. Atualizar o repositório após novas alterações
Sempre que fizer alterações e quiser atualizar o GitHub:
```sh
git add .
git commit -m "Descreva a alteração"
git push
```