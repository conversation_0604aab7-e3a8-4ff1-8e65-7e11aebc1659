#!/bin/bash

echo "Iniciando reparo abrangente do Xcode 16..."

# Fechar o Xcode se estiver aberto
echo "Fechando o Xcode..."
osascript -e 'tell application "Xcode" to quit'
sleep 2

# Recarregar o serviço de índice do Xcode
echo "Reiniciando serviços do Xcode..."
launchctl remove com.apple.dt.Xcode.sourcecontrol.agent 2>/dev/null || true
launchctl remove com.apple.dt.XcodeBuild 2>/dev/null || true
sleep 1
launchctl start com.apple.dt.Xcode.sourcecontrol.agent 2>/dev/null || true
launchctl start com.apple.dt.XcodeBuild 2>/dev/null || true

# Limpar arquivos temporários do Xcode
echo "Limpando arquivos temporários..."
rm -rf ~/Library/Developer/Xcode/iOS\ DeviceSupport/* 2>/dev/null || true
rm -rf ~/Library/Developer/Xcode/Archives/* 2>/dev/null || true
rm -rf ~/Library/Developer/Xcode/Products/* 2>/dev/null || true

# Preparar o projeto Flutter
echo "Limpando e preparando o projeto..."
flutter clean
flutter pub get

# Preparar o projeto iOS
echo "Preparando o projeto iOS..."
cd ios
rm -rf Pods Podfile.lock build
pod cache clean --all
pod install --repo-update

echo "Reparo concluído! Agora tente abrir o projeto no Xcode e construir manualmente."
echo "Quando estiver no Xcode:"
echo "1. Selecione a aba 'Runner' no navegador de projeto"
echo "2. Vá para 'Build Settings'"
echo "3. Certifique-se de que 'Enable User Script Sandboxing' esteja definido como 'NO'"
echo "4. Selecione o dispositivo desejado e clique em executar"
echo ""
echo "Para abrir o projeto no Xcode, execute: open ios/Runner.xcworkspace" 